/**
 * Setup Command for Form Application System
 * Interactive dashboard for complete system configuration
 */

const {
    Slash<PERSON>ommandBuilder,
    PermissionFlagsBits,
    ChannelType,
    MessageFlags,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder
} = require('discord.js');

const formApplicationStorage = require('../utils/formApplicationStorage');
const { FormConfigModels, QUESTION_TYPES } = require('../utils/formConfigModels');
const formRenderer = require('../utils/formRenderer');
const applicationProcessor = require('../utils/applicationProcessor');

// In-memory storage for setup sessions
const setupSessions = new Map();

// Cleanup old sessions every 30 minutes
setInterval(() => {
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30 minutes
    
    for (const [sessionId, session] of setupSessions.entries()) {
        if (now - session.createdAt > maxAge) {
            setupSessions.delete(sessionId);
            console.log(`[SETUP] Removed expired session: ${sessionId}`);
        }
    }
}, 30 * 60 * 1000);

module.exports = {
    data: new SlashCommandBuilder()
        .setName('application-setup')
        .setDescription('Comprehensive form application system - setup, management, and user submissions')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild),

    async execute(interaction) {
        try {
            // Create setup session
            const sessionId = `${interaction.user.id}_${Date.now()}`;
            const session = {
                userId: interaction.user.id,
                guildId: interaction.guild.id,
                createdAt: Date.now(),
                currentStep: 'main',
                formConfig: FormConfigModels.createFormConfig({
                    guildId: interaction.guild.id,
                    createdBy: interaction.user.id
                })
            };
            
            setupSessions.set(sessionId, session);
            
            // Show main dashboard
            await this.showMainDashboard(interaction, sessionId, false);
            
        } catch (error) {
            console.error('[SETUP] Error in setup command:', error);
            await interaction.reply({
                content: `❌ **Error**\nFailed to initialize setup dashboard: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Show the main setup dashboard
     */
    async showMainDashboard(interaction, sessionId, isUpdate = false) {
        const session = setupSessions.get(sessionId);
        if (!session) {
            return await this.handleExpiredSession(interaction);
        }

        const embed = new EmbedBuilder()
            .setTitle('🔧 Application System Dashboard')
            .setDescription(
                '**Welcome to the Comprehensive Application System Dashboard!**\n\n' +
                'This unified interface provides complete control over your form application system:\n' +
                '• **Setup & Configuration** - Create and manage forms\n' +
                '• **User Submissions** - Submit applications directly\n' +
                '• **Admin Review** - Review and manage applications\n' +
                '• **Analytics & Reports** - View statistics and export data\n' +
                '• **System Management** - Cleanup and maintenance\n\n' +
                '**Current Configuration:**'
            )
            .setColor(0x3498DB)
            .addFields(
                {
                    name: '📝 Form Name',
                    value: session.formConfig.name || '*Not set*',
                    inline: true
                },
                {
                    name: '📋 Questions',
                    value: `${session.formConfig.questions.length} configured`,
                    inline: true
                },
                {
                    name: '🔍 Verification',
                    value: session.formConfig.verificationEnabled ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                },
                {
                    name: '🎯 Target Channels',
                    value: session.formConfig.targetChannels.length > 0 ? 
                        `${session.formConfig.targetChannels.length} selected` : '*None selected*',
                    inline: true
                },
                {
                    name: '📊 Log Channel',
                    value: session.formConfig.logChannel ? 
                        `<#${session.formConfig.logChannel}>` : '*Not set*',
                    inline: true
                },
                {
                    name: '🏷️ Role Rules',
                    value: `${session.formConfig.roleAssignmentRules.length} configured`,
                    inline: true
                }
            )
            .setFooter({ 
                text: `Setup Session • ${session.formConfig.isActive ? 'Active' : 'Inactive'} • Session expires in 30 minutes`
            })
            .setTimestamp();

        const components = this.createMainDashboardComponents(sessionId, session);

        if (isUpdate) {
            await interaction.update({ embeds: [embed], components });
        } else {
            await interaction.reply({ embeds: [embed], components, flags: MessageFlags.Ephemeral });
        }
    },

    /**
     * Create main dashboard components
     */
    createMainDashboardComponents(sessionId, session) {
        // Configuration Section
        const configRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_basic_${sessionId}`)
                    .setLabel('Basic Settings')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⚙️'),
                new ButtonBuilder()
                    .setCustomId(`setup_questions_${sessionId}`)
                    .setLabel('Manage Questions')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId(`setup_verification_${sessionId}`)
                    .setLabel('Verification Setup')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔍')
            );

        // Advanced Configuration
        const advancedRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_roles_${sessionId}`)
                    .setLabel('Role Assignment')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🏷️'),
                new ButtonBuilder()
                    .setCustomId(`setup_channels_${sessionId}`)
                    .setLabel('Channel Settings')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📺'),
                new ButtonBuilder()
                    .setCustomId(`setup_processing_${sessionId}`)
                    .setLabel('Processing Options')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚡')
            );

        // User & Admin Actions
        const actionsRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_submit_application_${sessionId}`)
                    .setLabel('Submit Application')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('📋'),
                new ButtonBuilder()
                    .setCustomId(`setup_review_applications_${sessionId}`)
                    .setLabel('Review Applications')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🛡️'),
                new ButtonBuilder()
                    .setCustomId(`setup_statistics_${sessionId}`)
                    .setLabel('Statistics')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊')
            );

        // System Management
        const managementRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_export_data_${sessionId}`)
                    .setLabel('Export Data')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📤'),
                new ButtonBuilder()
                    .setCustomId(`setup_cleanup_${sessionId}`)
                    .setLabel('System Cleanup')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🧹'),
                new ButtonBuilder()
                    .setCustomId(`setup_preview_${sessionId}`)
                    .setLabel('Preview Form')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('👁️')
            );

        // Save/Cancel Row
        const saveRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_save_${sessionId}`)
                    .setLabel('Save Configuration')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('💾')
                    .setDisabled(!this.isConfigurationValid(session.formConfig)),
                new ButtonBuilder()
                    .setCustomId(`setup_cancel_${sessionId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        return [configRow, advancedRow, actionsRow, managementRow, saveRow];
    },

    /**
     * Check if configuration is valid for saving
     */
    isConfigurationValid(config) {
        return config.name && 
               config.name.trim() !== '' && 
               config.questions.length > 0 && 
               config.targetChannels.length > 0;
    },

    /**
     * Handle button interactions for setup dashboard
     */
    async handleSetupButton(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        
        if (parts.length < 3) {
            return await interaction.reply({
                content: '❌ Invalid button interaction',
                flags: MessageFlags.Ephemeral
            });
        }

        const action = parts[1];
        const sessionId = parts.slice(2).join('_');
        const session = setupSessions.get(sessionId);

        if (!session) {
            return await this.handleExpiredSession(interaction);
        }

        // Verify user permissions
        if (session.userId !== interaction.user.id) {
            return await interaction.reply({
                content: '❌ You can only interact with your own setup session',
                flags: MessageFlags.Ephemeral
            });
        }

        try {
            switch (action) {
                case 'basic':
                    await this.showBasicSettings(interaction, sessionId);
                    break;
                case 'questions':
                    await this.showQuestionManagement(interaction, sessionId);
                    break;
                case 'verification':
                    await this.showVerificationSettings(interaction, sessionId);
                    break;
                case 'roles':
                    await this.showRoleAssignment(interaction, sessionId);
                    break;
                case 'channels':
                    await this.showChannelSettings(interaction, sessionId);
                    break;
                case 'processing':
                    await this.showProcessingOptions(interaction, sessionId);
                    break;
                case 'preview':
                    await this.showFormPreview(interaction, sessionId);
                    break;
                case 'save':
                    await this.saveConfiguration(interaction, sessionId);
                    break;
                case 'cancel':
                    await this.cancelSetup(interaction, sessionId);
                    break;
                case 'submit':
                    if (parts[2] === 'application') {
                        await this.handleSubmitApplication(interaction, sessionId);
                    }
                    break;
                case 'review':
                    if (parts[2] === 'applications') {
                        await this.handleReviewApplications(interaction, sessionId);
                    }
                    break;
                case 'statistics':
                    await this.handleStatistics(interaction, sessionId);
                    break;
                case 'export':
                    if (parts[2] === 'data') {
                        await this.handleExportData(interaction, sessionId);
                    }
                    break;
                case 'cleanup':
                    await this.handleCleanup(interaction, sessionId);
                    break;
                case 'back':
                    if (parts[2] === 'main') {
                        await this.showMainDashboard(interaction, sessionId, true);
                    }
                    break;
                case 'form':
                    if (parts[2] === 'start') {
                        const configId = parts[4];
                        await this.handleFormStart(interaction, sessionId, configId);
                    }
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Unknown setup action',
                        flags: MessageFlags.Ephemeral
                    });
            }
        } catch (error) {
            console.error('[SETUP] Error handling button interaction:', error);
            await interaction.reply({
                content: `❌ **Error**\nFailed to process setup action: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Handle expired session
     */
    async handleExpiredSession(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('⏰ Session Expired')
            .setDescription(
                'Your setup session has expired. Please run `/setup` again to start a new configuration session.'
            )
            .setColor(0xE74C3C);

        const restartButton = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_restart')
                    .setLabel('Start New Setup')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔄')
            );

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp({ embeds: [embed], components: [restartButton], flags: MessageFlags.Ephemeral });
        } else {
            await interaction.reply({ embeds: [embed], components: [restartButton], flags: MessageFlags.Ephemeral });
        }
    },

    /**
     * Save configuration to storage
     */
    async saveConfiguration(interaction, sessionId) {
        const session = setupSessions.get(sessionId);
        
        try {
            // Validate configuration
            FormConfigModels.validateFormConfig(session.formConfig);
            
            // Save to storage
            const configId = await formApplicationStorage.saveFormConfig(
                session.guildId, 
                session.formConfig
            );
            
            // Log the action
            await formApplicationStorage.addAuditLog({
                action: 'form_config_created',
                guildId: session.guildId,
                userId: session.userId,
                configId: configId,
                details: {
                    formName: session.formConfig.name,
                    questionCount: session.formConfig.questions.length,
                    verificationEnabled: session.formConfig.verificationEnabled
                }
            });
            
            // Clean up session
            setupSessions.delete(sessionId);
            
            const embed = new EmbedBuilder()
                .setTitle('✅ Configuration Saved Successfully!')
                .setDescription(
                    `**Form "${session.formConfig.name}" has been created and saved.**\n\n` +
                    `**Configuration ID:** \`${configId}\`\n` +
                    `**Questions:** ${session.formConfig.questions.length}\n` +
                    `**Verification:** ${session.formConfig.verificationEnabled ? 'Enabled' : 'Disabled'}\n` +
                    `**Target Channels:** ${session.formConfig.targetChannels.length}\n` +
                    `**Role Rules:** ${session.formConfig.roleAssignmentRules.length}\n\n` +
                    `Users can now submit applications using this form configuration. ` +
                    `Use \`/form-admin\` to manage applications and review submissions.`
                )
                .setColor(0x27AE60)
                .setFooter({ text: 'Form Application System' })
                .setTimestamp();

            await interaction.update({ embeds: [embed], components: [] });
            
        } catch (error) {
            console.error('[SETUP] Error saving configuration:', error);
            await interaction.reply({
                content: `❌ **Error Saving Configuration**\n${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Cancel setup and clean up session
     */
    async cancelSetup(interaction, sessionId) {
        setupSessions.delete(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('❌ Setup Cancelled')
            .setDescription('Form application system setup has been cancelled. No changes were saved.')
            .setColor(0xE74C3C);

        await interaction.update({ embeds: [embed], components: [] });
    },

    // ==================== CONFIGURATION SCREENS ====================

    /**
     * Show basic settings configuration
     */
    async showBasicSettings(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('⚙️ Basic Settings')
            .setDescription(
                'Configure the fundamental settings for your form application system.\n\n' +
                '**Current Settings:**'
            )
            .setColor(0x3498DB)
            .addFields(
                {
                    name: '📝 Form Name',
                    value: session.formConfig.name || '*Not set*',
                    inline: true
                },
                {
                    name: '📄 Description',
                    value: session.formConfig.description || '*Not set*',
                    inline: true
                },
                {
                    name: '🔄 Status',
                    value: session.formConfig.isActive ? '✅ Active' : '❌ Inactive',
                    inline: true
                },
                {
                    name: '⚡ Auto Approve',
                    value: session.formConfig.autoApprove ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                },
                {
                    name: '👥 Admin Review Required',
                    value: session.formConfig.requireAdminReview ? '✅ Yes' : '❌ No',
                    inline: true
                },
                {
                    name: '🔄 Allow Resubmission',
                    value: session.formConfig.allowResubmission ? '✅ Yes' : '❌ No',
                    inline: true
                }
            )
            .setFooter({ text: 'Use the buttons below to modify these settings' });

        const components = this.createBasicSettingsComponents(sessionId);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create basic settings components
     */
    createBasicSettingsComponents(sessionId) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_edit_name_${sessionId}`)
                    .setLabel('Edit Name')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId(`setup_edit_description_${sessionId}`)
                    .setLabel('Edit Description')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📄'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_active_${sessionId}`)
                    .setLabel('Toggle Active')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔄')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_auto_approve_${sessionId}`)
                    .setLabel('Toggle Auto Approve')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚡'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_admin_review_${sessionId}`)
                    .setLabel('Toggle Admin Review')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('👥'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_resubmission_${sessionId}`)
                    .setLabel('Toggle Resubmission')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔄')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1, row2, row3];
    },

    /**
     * Show question management screen
     */
    async showQuestionManagement(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('📝 Question Management')
            .setDescription(
                'Manage the questions that will appear in your application form.\n\n' +
                '**Question Types Available:**\n' +
                '• Text Input - Simple text responses\n' +
                '• Email - Email address validation\n' +
                '• Phone - Phone number validation\n' +
                '• Date - Date picker\n' +
                '• Number - Numeric input\n' +
                '• Dropdown - Single selection from options\n' +
                '• Multiple Choice - Single selection with radio buttons\n' +
                '• Checkbox - Multiple selections\n' +
                '• Text Area - Long text responses\n\n' +
                `**Current Questions (${session.formConfig.questions.length}):**`
            )
            .setColor(0x9B59B6);

        // Add fields for existing questions
        if (session.formConfig.questions.length > 0) {
            session.formConfig.questions
                .sort((a, b) => a.order - b.order)
                .slice(0, 10) // Limit to first 10 questions for display
                .forEach((question, index) => {
                    embed.addFields({
                        name: `${index + 1}. ${question.label}`,
                        value: `Type: ${question.type}${question.required ? ' (Required)' : ''}${question.isVerificationCriteria ? ' 🔍' : ''}`,
                        inline: false
                    });
                });

            if (session.formConfig.questions.length > 10) {
                embed.addFields({
                    name: '...',
                    value: `And ${session.formConfig.questions.length - 10} more questions`,
                    inline: false
                });
            }
        } else {
            embed.addFields({
                name: 'No Questions',
                value: 'Click "Add Question" to create your first question.',
                inline: false
            });
        }

        const components = this.createQuestionManagementComponents(sessionId, session);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create question management components
     */
    createQuestionManagementComponents(sessionId, session) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_add_question_${sessionId}`)
                    .setLabel('Add Question')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId(`setup_edit_question_${sessionId}`)
                    .setLabel('Edit Question')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('✏️')
                    .setDisabled(session.formConfig.questions.length === 0),
                new ButtonBuilder()
                    .setCustomId(`setup_delete_question_${sessionId}`)
                    .setLabel('Delete Question')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🗑️')
                    .setDisabled(session.formConfig.questions.length === 0)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_reorder_questions_${sessionId}`)
                    .setLabel('Reorder Questions')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔄')
                    .setDisabled(session.formConfig.questions.length < 2),
                new ButtonBuilder()
                    .setCustomId(`setup_import_questions_${sessionId}`)
                    .setLabel('Import Questions')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📥'),
                new ButtonBuilder()
                    .setCustomId(`setup_export_questions_${sessionId}`)
                    .setLabel('Export Questions')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📤')
                    .setDisabled(session.formConfig.questions.length === 0)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1, row2, row3];
    },

    /**
     * Show verification settings screen
     */
    async showVerificationSettings(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const verificationQuestions = session.formConfig.questions.filter(q => q.isVerificationCriteria);

        const embed = new EmbedBuilder()
            .setTitle('🔍 Verification Settings')
            .setDescription(
                'Configure data verification to automatically check form responses against stored data.\n\n' +
                '**How Verification Works:**\n' +
                '• Mark questions as "verification criteria"\n' +
                '• Bot compares answers against data in specified channels\n' +
                '• Uses configurable similarity thresholds (70% default, 85-90% strict)\n' +
                '• Supports multiple matching algorithms\n' +
                '• Automatic approval for verified applications\n\n' +
                '**Current Settings:**'
            )
            .setColor(0xE67E22)
            .addFields(
                {
                    name: '🔍 Verification Status',
                    value: session.formConfig.verificationEnabled ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                },
                {
                    name: '📊 Similarity Threshold',
                    value: `${session.formConfig.similarityThreshold}%`,
                    inline: true
                },
                {
                    name: '🎯 Strict Matching',
                    value: session.formConfig.strictMatching ? '✅ Enabled (85-90%)' : '❌ Disabled',
                    inline: true
                },
                {
                    name: '📺 Verification Channels',
                    value: session.formConfig.verificationChannels.length > 0 ?
                        session.formConfig.verificationChannels.map(id => `<#${id}>`).join(', ') :
                        '*None selected*',
                    inline: false
                },
                {
                    name: '❓ Verification Questions',
                    value: verificationQuestions.length > 0 ?
                        verificationQuestions.map(q => `• ${q.label}`).join('\n') :
                        '*No questions marked for verification*',
                    inline: false
                }
            );

        const components = this.createVerificationSettingsComponents(sessionId, session);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create verification settings components
     */
    createVerificationSettingsComponents(sessionId, session) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_verification_${sessionId}`)
                    .setLabel('Toggle Verification')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔍'),
                new ButtonBuilder()
                    .setCustomId(`setup_set_threshold_${sessionId}`)
                    .setLabel('Set Threshold')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_strict_${sessionId}`)
                    .setLabel('Toggle Strict Mode')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎯')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_select_verification_channels_${sessionId}`)
                    .setLabel('Select Verification Channels')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📺'),
                new ButtonBuilder()
                    .setCustomId(`setup_manage_verification_questions_${sessionId}`)
                    .setLabel('Manage Verification Questions')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❓')
                    .setDisabled(session.formConfig.questions.length === 0)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_test_verification_${sessionId}`)
                    .setLabel('Test Verification')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🧪')
                    .setDisabled(!session.formConfig.verificationEnabled || session.formConfig.verificationChannels.length === 0),
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1, row2, row3];
    },

    /**
     * Show role assignment configuration
     */
    async showRoleAssignment(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('🏷️ Role Assignment Configuration')
            .setDescription(
                'Configure automatic role assignment based on form responses.\n\n' +
                '**Assignment Rules:**\n' +
                '• Create rules based on specific answers\n' +
                '• Support multiple conditions per rule\n' +
                '• Assign different roles for different answer combinations\n' +
                '• Set priority for conflicting rules\n' +
                '• Configure default role for unmatched applications\n\n' +
                `**Current Configuration:**`
            )
            .setColor(0xF39C12)
            .addFields(
                {
                    name: '📋 Assignment Rules',
                    value: session.formConfig.roleAssignmentRules.length > 0 ?
                        `${session.formConfig.roleAssignmentRules.length} rules configured` :
                        '*No rules configured*',
                    inline: true
                },
                {
                    name: '🎯 Default Role',
                    value: session.formConfig.defaultRole ?
                        `<@&${session.formConfig.defaultRole}>` :
                        '*Not set*',
                    inline: true
                },
                {
                    name: '🔢 Max Roles Per User',
                    value: session.formConfig.maxRolesPerUser.toString(),
                    inline: true
                }
            );

        // Add details for existing rules
        if (session.formConfig.roleAssignmentRules.length > 0) {
            const ruleDetails = session.formConfig.roleAssignmentRules
                .slice(0, 5) // Show first 5 rules
                .map((rule, index) => {
                    const question = session.formConfig.questions.find(q => q.id === rule.questionId);
                    return `${index + 1}. **${rule.name}**\n` +
                           `   Question: ${question?.label || 'Unknown'}\n` +
                           `   Condition: ${rule.condition}\n` +
                           `   Role: ${rule.roleName || 'Unknown'}`;
                }).join('\n\n');

            embed.addFields({
                name: 'Rule Details',
                value: ruleDetails,
                inline: false
            });

            if (session.formConfig.roleAssignmentRules.length > 5) {
                embed.addFields({
                    name: '...',
                    value: `And ${session.formConfig.roleAssignmentRules.length - 5} more rules`,
                    inline: false
                });
            }
        }

        const components = this.createRoleAssignmentComponents(sessionId, session);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create role assignment components
     */
    createRoleAssignmentComponents(sessionId, session) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_add_role_rule_${sessionId}`)
                    .setLabel('Add Role Rule')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('➕')
                    .setDisabled(session.formConfig.questions.length === 0),
                new ButtonBuilder()
                    .setCustomId(`setup_edit_role_rule_${sessionId}`)
                    .setLabel('Edit Role Rule')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('✏️')
                    .setDisabled(session.formConfig.roleAssignmentRules.length === 0),
                new ButtonBuilder()
                    .setCustomId(`setup_delete_role_rule_${sessionId}`)
                    .setLabel('Delete Role Rule')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🗑️')
                    .setDisabled(session.formConfig.roleAssignmentRules.length === 0)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_set_default_role_${sessionId}`)
                    .setLabel('Set Default Role')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎯'),
                new ButtonBuilder()
                    .setCustomId(`setup_set_max_roles_${sessionId}`)
                    .setLabel('Set Max Roles')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔢'),
                new ButtonBuilder()
                    .setCustomId(`setup_test_role_assignment_${sessionId}`)
                    .setLabel('Test Assignment')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🧪')
                    .setDisabled(session.formConfig.roleAssignmentRules.length === 0)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1, row2, row3];
    },

    /**
     * Show channel settings configuration
     */
    async showChannelSettings(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('📺 Channel Settings')
            .setDescription(
                'Configure where forms can be submitted and where logs are sent.\n\n' +
                '**Channel Types:**\n' +
                '• **Target Channels** - Where users can submit applications\n' +
                '• **Log Channel** - Where application logs and admin notifications are sent\n' +
                '• **Verification Channels** - Where verification data is stored\n\n' +
                '**Current Settings:**'
            )
            .setColor(0x8E44AD)
            .addFields(
                {
                    name: '🎯 Target Channels',
                    value: session.formConfig.targetChannels.length > 0 ?
                        session.formConfig.targetChannels.map(id => `<#${id}>`).join('\n') :
                        '*None selected*',
                    inline: true
                },
                {
                    name: '📊 Log Channel',
                    value: session.formConfig.logChannel ?
                        `<#${session.formConfig.logChannel}>` :
                        '*Not set*',
                    inline: true
                },
                {
                    name: '🔍 Verification Channels',
                    value: session.formConfig.verificationChannels.length > 0 ?
                        session.formConfig.verificationChannels.map(id => `<#${id}>`).join('\n') :
                        '*None selected*',
                    inline: true
                }
            );

        const components = this.createChannelSettingsComponents(sessionId);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create channel settings components
     */
    createChannelSettingsComponents(sessionId) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_select_target_channels_${sessionId}`)
                    .setLabel('Select Target Channels')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎯'),
                new ButtonBuilder()
                    .setCustomId(`setup_select_log_channel_${sessionId}`)
                    .setLabel('Select Log Channel')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊'),
                new ButtonBuilder()
                    .setCustomId(`setup_select_verification_channels_${sessionId}`)
                    .setLabel('Select Verification Channels')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔍')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1, row2];
    },

    /**
     * Show processing options configuration
     */
    async showProcessingOptions(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('⚡ Processing Options')
            .setDescription(
                'Configure how applications are processed and what notifications are sent.\n\n' +
                '**Processing Settings:**'
            )
            .setColor(0x16A085)
            .addFields(
                {
                    name: '⚡ Auto Approve',
                    value: session.formConfig.autoApprove ?
                        '✅ Enabled - Applications passing verification are auto-approved' :
                        '❌ Disabled - All applications require manual review',
                    inline: false
                },
                {
                    name: '👥 Admin Review Required',
                    value: session.formConfig.requireAdminReview ?
                        '✅ Yes - Admins must review all applications' :
                        '❌ No - Applications can be processed automatically',
                    inline: false
                },
                {
                    name: '🔄 Allow Resubmission',
                    value: session.formConfig.allowResubmission ?
                        '✅ Yes - Users can resubmit if rejected' :
                        '❌ No - One submission per user',
                    inline: false
                },
                {
                    name: '📧 DM on Approval',
                    value: session.formConfig.sendDMOnApproval ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                },
                {
                    name: '📧 DM on Rejection',
                    value: session.formConfig.sendDMOnRejection ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                },
                {
                    name: '✅ Approval Message',
                    value: `"${session.formConfig.approvalMessage}"`,
                    inline: false
                },
                {
                    name: '❌ Rejection Message',
                    value: `"${session.formConfig.rejectionMessage}"`,
                    inline: false
                }
            );

        const components = this.createProcessingOptionsComponents(sessionId);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create processing options components
     */
    createProcessingOptionsComponents(sessionId) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_auto_approve_${sessionId}`)
                    .setLabel('Toggle Auto Approve')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚡'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_admin_review_${sessionId}`)
                    .setLabel('Toggle Admin Review')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('👥'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_resubmission_${sessionId}`)
                    .setLabel('Toggle Resubmission')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔄')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_dm_approval_${sessionId}`)
                    .setLabel('Toggle DM on Approval')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📧'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_dm_rejection_${sessionId}`)
                    .setLabel('Toggle DM on Rejection')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📧')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_edit_approval_message_${sessionId}`)
                    .setLabel('Edit Approval Message')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('✅'),
                new ButtonBuilder()
                    .setCustomId(`setup_edit_rejection_message_${sessionId}`)
                    .setLabel('Edit Rejection Message')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❌'),
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1, row2, row3];
    },

    /**
     * Show form preview
     */
    async showFormPreview(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('👁️ Form Preview')
            .setDescription(
                `**Preview of "${session.formConfig.name}"**\n\n` +
                (session.formConfig.description ? `${session.formConfig.description}\n\n` : '') +
                '**Questions:**'
            )
            .setColor(0x27AE60);

        if (session.formConfig.questions.length > 0) {
            session.formConfig.questions
                .sort((a, b) => a.order - b.order)
                .forEach((question, index) => {
                    let questionText = `**${index + 1}. ${question.label}**`;
                    if (question.required) questionText += ' *';
                    if (question.isVerificationCriteria) questionText += ' 🔍';

                    let questionDetails = `Type: ${question.type}`;
                    if (question.description) questionDetails += `\nDescription: ${question.description}`;
                    if (question.placeholder) questionDetails += `\nPlaceholder: ${question.placeholder}`;
                    if (question.options && question.options.length > 0) {
                        questionDetails += `\nOptions: ${question.options.map(opt => opt.label || opt.value).join(', ')}`;
                    }

                    embed.addFields({
                        name: questionText,
                        value: questionDetails,
                        inline: false
                    });
                });
        } else {
            embed.addFields({
                name: 'No Questions',
                value: 'No questions have been configured yet.',
                inline: false
            });
        }

        embed.addFields({
            name: 'Configuration Summary',
            value: `• **Questions:** ${session.formConfig.questions.length}\n` +
                   `• **Verification:** ${session.formConfig.verificationEnabled ? 'Enabled' : 'Disabled'}\n` +
                   `• **Target Channels:** ${session.formConfig.targetChannels.length}\n` +
                   `• **Role Rules:** ${session.formConfig.roleAssignmentRules.length}\n` +
                   `• **Auto Approve:** ${session.formConfig.autoApprove ? 'Yes' : 'No'}\n` +
                   `• **Admin Review:** ${session.formConfig.requireAdminReview ? 'Required' : 'Optional'}`,
            inline: false
        });

        const components = this.createFormPreviewComponents(sessionId, session);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create form preview components
     */
    createFormPreviewComponents(sessionId, session) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_test_form_${sessionId}`)
                    .setLabel('Test Form Submission')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🧪')
                    .setDisabled(session.formConfig.questions.length === 0),
                new ButtonBuilder()
                    .setCustomId(`setup_export_config_${sessionId}`)
                    .setLabel('Export Configuration')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📤'),
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1];
    },

    // ==================== INTEGRATED FUNCTIONALITY ====================

    /**
     * Handle submit application (integrated from /form command)
     */
    async handleSubmitApplication(interaction, sessionId) {
        try {
            const session = setupSessions.get(sessionId);
            if (!session) {
                return await this.handleExpiredSession(interaction);
            }

            // Get available forms for this guild
            const guildConfigs = await formApplicationStorage.getGuildFormConfigs(session.guildId);
            const activeConfigs = Object.values(guildConfigs).filter(config =>
                config.isActive &&
                (config.targetChannels.length === 0 || config.targetChannels.includes(interaction.channel.id))
            );

            if (activeConfigs.length === 0) {
                return await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ No Forms Available')
                            .setDescription('There are no active forms configured for this channel. Please configure a form first using the Basic Settings.')
                            .setColor(0xE74C3C)
                    ],
                    components: [
                        new ActionRowBuilder()
                            .addComponents(
                                new ButtonBuilder()
                                    .setCustomId(`setup_back_main_${sessionId}`)
                                    .setLabel('Back to Dashboard')
                                    .setStyle(ButtonStyle.Primary)
                                    .setEmoji('⬅️')
                            )
                    ]
                });
            }

            // Show available forms
            const embed = new EmbedBuilder()
                .setTitle('📋 Submit Application')
                .setDescription('Select a form to submit your application:')
                .setColor(0x3498DB);

            activeConfigs.forEach((config, index) => {
                const verificationText = config.verificationEnabled ? '🔍 Verification Required' : '📝 No Verification';
                const reviewText = config.requireAdminReview ? '👥 Admin Review Required' : '⚡ Auto-Processing';

                embed.addFields({
                    name: `${index + 1}. ${config.name}`,
                    value: `${config.description || 'No description provided'}\n\n` +
                           `• **Questions:** ${config.questions.length}\n` +
                           `• **Processing:** ${verificationText}, ${reviewText}\n` +
                           `• **Resubmission:** ${config.allowResubmission ? 'Allowed' : 'Not Allowed'}`,
                    inline: false
                });
            });

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`setup_form_select_${sessionId}`)
                .setPlaceholder('Select a form to submit...')
                .addOptions(
                    activeConfigs.map((config, index) =>
                        new StringSelectMenuOptionBuilder()
                            .setLabel(config.name)
                            .setValue(config.id)
                            .setDescription(`${config.questions.length} questions • ${config.verificationEnabled ? 'Verification required' : 'No verification'}`)
                            .setEmoji('📋')
                    )
                );

            const components = [
                new ActionRowBuilder().addComponents(selectMenu),
                new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`setup_back_main_${sessionId}`)
                            .setLabel('Back to Dashboard')
                            .setStyle(ButtonStyle.Primary)
                            .setEmoji('⬅️')
                    )
            ];

            await interaction.update({ embeds: [embed], components });

        } catch (error) {
            console.error('[SETUP] Error handling submit application:', error);
            await interaction.reply({
                content: `❌ Error loading forms: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Handle review applications (integrated from /form-admin review)
     */
    async handleReviewApplications(interaction, sessionId) {
        try {
            const session = setupSessions.get(sessionId);
            if (!session) {
                return await this.handleExpiredSession(interaction);
            }

            // Import admin review system
            const adminReviewSystem = require('../utils/adminReviewSystem');

            // Create admin dashboard with filters
            const filters = {};
            await adminReviewSystem.createAdminDashboard(interaction, filters);

        } catch (error) {
            console.error('[SETUP] Error handling review applications:', error);
            await interaction.reply({
                content: `❌ Error loading application review: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Handle statistics (integrated from /form-admin stats)
     */
    async handleStatistics(interaction, sessionId) {
        try {
            const session = setupSessions.get(sessionId);
            if (!session) {
                return await this.handleExpiredSession(interaction);
            }

            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('⏳ Loading Statistics...')
                        .setDescription('Generating application statistics and analytics...')
                        .setColor(0xF39C12)
                ],
                components: []
            });

            const guildId = session.guildId;
            const applications = await formApplicationStorage.getApplicationsByGuild(guildId);
            const guildConfigs = await formApplicationStorage.getGuildFormConfigs(guildId);
            const storageStats = await formApplicationStorage.getStorageStats();

            // Calculate statistics
            const stats = {
                total: applications.length,
                pending: applications.filter(app => app.status === 'pending').length,
                approved: applications.filter(app => app.status === 'approved').length,
                rejected: applications.filter(app => app.status === 'rejected').length,
                underReview: applications.filter(app => app.status === 'under_review').length,
                requiresVerification: applications.filter(app => app.status === 'requires_verification').length
            };

            // Calculate time-based statistics
            const now = Date.now();
            const oneDayAgo = now - (24 * 60 * 60 * 1000);
            const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000);
            const oneMonthAgo = now - (30 * 24 * 60 * 60 * 1000);

            const recentStats = {
                today: applications.filter(app => app.submittedAt >= oneDayAgo).length,
                thisWeek: applications.filter(app => app.submittedAt >= oneWeekAgo).length,
                thisMonth: applications.filter(app => app.submittedAt >= oneMonthAgo).length
            };

            const embed = new EmbedBuilder()
                .setTitle('📊 Application Statistics')
                .setDescription(`Statistics for ${interaction.guild.name}`)
                .setColor(0x3498DB)
                .addFields(
                    {
                        name: '📋 Total Applications',
                        value: stats.total.toString(),
                        inline: true
                    },
                    {
                        name: '✅ Approved',
                        value: `${stats.approved} (${stats.total > 0 ? Math.round((stats.approved / stats.total) * 100) : 0}%)`,
                        inline: true
                    },
                    {
                        name: '❌ Rejected',
                        value: `${stats.rejected} (${stats.total > 0 ? Math.round((stats.rejected / stats.total) * 100) : 0}%)`,
                        inline: true
                    },
                    {
                        name: '⏳ Pending',
                        value: stats.pending.toString(),
                        inline: true
                    },
                    {
                        name: '👥 Under Review',
                        value: stats.underReview.toString(),
                        inline: true
                    },
                    {
                        name: '🔍 Requires Verification',
                        value: stats.requiresVerification.toString(),
                        inline: true
                    },
                    {
                        name: '📅 Recent Activity',
                        value: `• Today: ${recentStats.today}\n• This Week: ${recentStats.thisWeek}\n• This Month: ${recentStats.thisMonth}`,
                        inline: false
                    },
                    {
                        name: '🔧 System Statistics',
                        value: `• Active Forms: ${Object.keys(guildConfigs).length}\n` +
                               `• Storage Cache: ${storageStats.cacheSize.applications} applications cached\n` +
                               `• Audit Log Entries: ${storageStats.auditLogEntries}`,
                        inline: false
                    }
                )
                .setFooter({ text: 'Application System Statistics' })
                .setTimestamp();

            await interaction.followUp({
                embeds: [embed],
                components: [
                    new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId(`setup_back_main_${sessionId}`)
                                .setLabel('Back to Dashboard')
                                .setStyle(ButtonStyle.Primary)
                                .setEmoji('⬅️')
                        )
                ],
                flags: MessageFlags.Ephemeral
            });

        } catch (error) {
            console.error('[SETUP] Error handling statistics:', error);
            await interaction.followUp({
                content: `❌ Error generating statistics: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Handle export data (integrated from /form-admin export)
     */
    async handleExportData(interaction, sessionId) {
        try {
            const session = setupSessions.get(sessionId);
            if (!session) {
                return await this.handleExpiredSession(interaction);
            }

            const embed = new EmbedBuilder()
                .setTitle('📤 Export Application Data')
                .setDescription('Choose the format for exporting application data:')
                .setColor(0x3498DB)
                .addFields(
                    {
                        name: '📊 CSV Format',
                        value: 'Spreadsheet-compatible format for analysis',
                        inline: true
                    },
                    {
                        name: '📋 JSON Format',
                        value: 'Complete data with all details',
                        inline: true
                    }
                );

            const components = [
                new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`setup_export_csv_${sessionId}`)
                            .setLabel('Export as CSV')
                            .setStyle(ButtonStyle.Success)
                            .setEmoji('📊'),
                        new ButtonBuilder()
                            .setCustomId(`setup_export_json_${sessionId}`)
                            .setLabel('Export as JSON')
                            .setStyle(ButtonStyle.Success)
                            .setEmoji('📋'),
                        new ButtonBuilder()
                            .setCustomId(`setup_back_main_${sessionId}`)
                            .setLabel('Back to Dashboard')
                            .setStyle(ButtonStyle.Primary)
                            .setEmoji('⬅️')
                    )
            ];

            await interaction.update({ embeds: [embed], components });

        } catch (error) {
            console.error('[SETUP] Error handling export data:', error);
            await interaction.reply({
                content: `❌ Error loading export options: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Handle cleanup (integrated from /form-admin cleanup)
     */
    async handleCleanup(interaction, sessionId) {
        try {
            const session = setupSessions.get(sessionId);
            if (!session) {
                return await this.handleExpiredSession(interaction);
            }

            const embed = new EmbedBuilder()
                .setTitle('🧹 System Cleanup')
                .setDescription(
                    'Clean up old applications and optimize system performance.\n\n' +
                    '**Available Cleanup Options:**'
                )
                .setColor(0xE67E22)
                .addFields(
                    {
                        name: '🗑️ Delete Old Applications',
                        value: 'Remove applications older than specified days',
                        inline: false
                    },
                    {
                        name: '🧹 Clear Cache',
                        value: 'Clear system cache to free memory',
                        inline: false
                    },
                    {
                        name: '📋 Optimize Storage',
                        value: 'Optimize data storage and indexing',
                        inline: false
                    }
                );

            const components = [
                new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`setup_cleanup_old_${sessionId}`)
                            .setLabel('Delete Old Applications')
                            .setStyle(ButtonStyle.Danger)
                            .setEmoji('🗑️'),
                        new ButtonBuilder()
                            .setCustomId(`setup_cleanup_cache_${sessionId}`)
                            .setLabel('Clear Cache')
                            .setStyle(ButtonStyle.Secondary)
                            .setEmoji('🧹'),
                        new ButtonBuilder()
                            .setCustomId(`setup_cleanup_optimize_${sessionId}`)
                            .setLabel('Optimize Storage')
                            .setStyle(ButtonStyle.Secondary)
                            .setEmoji('📋')
                    ),
                new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`setup_back_main_${sessionId}`)
                            .setLabel('Back to Dashboard')
                            .setStyle(ButtonStyle.Primary)
                            .setEmoji('⬅️')
                    )
            ];

            await interaction.update({ embeds: [embed], components });

        } catch (error) {
            console.error('[SETUP] Error handling cleanup:', error);
            await interaction.reply({
                content: `❌ Error loading cleanup options: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Handle form selection from dropdown
     */
    async handleFormSelection(interaction, sessionId) {
        try {
            const configId = interaction.values[0];
            const formConfig = await formApplicationStorage.getFormConfig(interaction.guild.id, configId);

            if (!formConfig) {
                return await interaction.reply({
                    content: '❌ Form configuration not found.',
                    flags: MessageFlags.Ephemeral
                });
            }

            // Check if user has already submitted an application for this form
            if (!formConfig.allowResubmission) {
                const existingApplications = await formApplicationStorage.getApplicationsByGuild(interaction.guild.id, {
                    configId: formConfig.id,
                    userId: interaction.user.id
                });

                if (existingApplications.length > 0) {
                    const latestApp = existingApplications[0];
                    return await interaction.update({
                        embeds: [
                            new EmbedBuilder()
                                .setTitle('❌ Application Already Submitted')
                                .setDescription(
                                    `You have already submitted an application for "${formConfig.name}".\n\n` +
                                    `**Status:** ${this.getStatusDisplay(latestApp.status)}\n` +
                                    `**Submitted:** <t:${Math.floor(latestApp.submittedAt / 1000)}:R>\n\n` +
                                    'Resubmission is not allowed for this form.'
                                )
                                .setColor(0xE74C3C)
                        ],
                        components: [
                            new ActionRowBuilder()
                                .addComponents(
                                    new ButtonBuilder()
                                        .setCustomId(`setup_back_main_${sessionId}`)
                                        .setLabel('Back to Dashboard')
                                        .setStyle(ButtonStyle.Primary)
                                        .setEmoji('⬅️')
                                )
                        ]
                    });
                }
            }

            await this.startFormSubmission(interaction, formConfig, sessionId);

        } catch (error) {
            console.error('[SETUP] Error handling form selection:', error);
            await interaction.reply({
                content: `❌ Error loading form: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Start form submission process
     */
    async startFormSubmission(interaction, formConfig, sessionId) {
        try {
            // Check if form has questions
            if (formConfig.questions.length === 0) {
                return await interaction.update({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('❌ Form Not Ready')
                            .setDescription('This form has no questions configured. Please configure questions first using the Manage Questions option.')
                            .setColor(0xE74C3C)
                    ],
                    components: [
                        new ActionRowBuilder()
                            .addComponents(
                                new ButtonBuilder()
                                    .setCustomId(`setup_back_main_${sessionId}`)
                                    .setLabel('Back to Dashboard')
                                    .setStyle(ButtonStyle.Primary)
                                    .setEmoji('⬅️')
                            )
                    ]
                });
            }

            // Show form introduction
            const embed = new EmbedBuilder()
                .setTitle(`📋 ${formConfig.name}`)
                .setDescription(
                    (formConfig.description ? `${formConfig.description}\n\n` : '') +
                    '**Form Information:**\n' +
                    `• **Questions:** ${formConfig.questions.length}\n` +
                    `• **Verification:** ${formConfig.verificationEnabled ? '🔍 Required' : '❌ Not Required'}\n` +
                    `• **Processing:** ${formConfig.autoApprove ? '⚡ Automatic' : '👥 Manual Review'}\n` +
                    `• **Estimated Time:** ${this.estimateFormTime(formConfig.questions.length)}\n\n` +
                    '**Instructions:**\n' +
                    '• Answer all required questions (marked with *)\n' +
                    '• You can navigate back and forth between questions\n' +
                    '• Review your answers before submitting\n' +
                    (formConfig.verificationEnabled ? '• Some answers will be verified against stored data\n' : '') +
                    '• You will receive a notification when your application is processed\n\n' +
                    'Click "Start Application" to begin.'
                )
                .setColor(0x3498DB)
                .setFooter({ text: 'Application System' })
                .setTimestamp();

            const startButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`setup_form_start_${sessionId}_${formConfig.id}`)
                        .setLabel('Start Application')
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('🚀'),
                    new ButtonBuilder()
                        .setCustomId(`setup_back_main_${sessionId}`)
                        .setLabel('Back to Dashboard')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('⬅️')
                );

            await interaction.update({ embeds: [embed], components: [startButton] });

        } catch (error) {
            console.error('[SETUP] Error starting form submission:', error);
            throw error;
        }
    },

    /**
     * Handle form start button
     */
    async handleFormStart(interaction, sessionId, configId) {
        try {
            const formConfig = await formApplicationStorage.getFormConfig(interaction.guild.id, configId);

            if (!formConfig) {
                return await interaction.reply({
                    content: '❌ Form configuration not found.',
                    flags: MessageFlags.Ephemeral
                });
            }

            // Start the form rendering process
            const formResult = await formRenderer.renderForm(formConfig, interaction);

            await interaction.update({
                embeds: formResult.embeds,
                components: formResult.components
            });

        } catch (error) {
            console.error('[SETUP] Error handling form start:', error);
            await interaction.reply({
                content: `❌ Error starting form: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Estimate form completion time
     */
    estimateFormTime(questionCount) {
        const timePerQuestion = 30; // seconds
        const totalSeconds = questionCount * timePerQuestion;
        const minutes = Math.ceil(totalSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''}`;
    },

    /**
     * Get status display text
     */
    getStatusDisplay(status) {
        const displays = {
            'pending': '⏳ Pending',
            'approved': '✅ Approved',
            'rejected': '❌ Rejected',
            'under_review': '👥 Under Review',
            'requires_verification': '🔍 Requires Verification'
        };
        return displays[status] || status;
    }
};

// Export setup sessions for use by other modules
module.exports.setupSessions = setupSessions;
