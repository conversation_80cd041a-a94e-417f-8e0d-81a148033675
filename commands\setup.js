/**
 * Setup Command for Form Application System
 * Interactive dashboard for complete system configuration
 */

const {
    Slash<PERSON>ommandBuilder,
    PermissionFlagsBits,
    ChannelType,
    MessageFlags,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder
} = require('discord.js');

const formApplicationStorage = require('../utils/formApplicationStorage');
const { FormConfigModels, QUESTION_TYPES } = require('../utils/formConfigModels');

// In-memory storage for setup sessions
const setupSessions = new Map();

// Cleanup old sessions every 30 minutes
setInterval(() => {
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30 minutes
    
    for (const [sessionId, session] of setupSessions.entries()) {
        if (now - session.createdAt > maxAge) {
            setupSessions.delete(sessionId);
            console.log(`[SETUP] Removed expired session: ${sessionId}`);
        }
    }
}, 30 * 60 * 1000);

module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup')
        .setDescription('Configure the form application system with an interactive dashboard')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild),

    async execute(interaction) {
        try {
            // Create setup session
            const sessionId = `${interaction.user.id}_${Date.now()}`;
            const session = {
                userId: interaction.user.id,
                guildId: interaction.guild.id,
                createdAt: Date.now(),
                currentStep: 'main',
                formConfig: FormConfigModels.createFormConfig({
                    guildId: interaction.guild.id,
                    createdBy: interaction.user.id
                })
            };
            
            setupSessions.set(sessionId, session);
            
            // Show main dashboard
            await this.showMainDashboard(interaction, sessionId, false);
            
        } catch (error) {
            console.error('[SETUP] Error in setup command:', error);
            await interaction.reply({
                content: `❌ **Error**\nFailed to initialize setup dashboard: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Show the main setup dashboard
     */
    async showMainDashboard(interaction, sessionId, isUpdate = false) {
        const session = setupSessions.get(sessionId);
        if (!session) {
            return await this.handleExpiredSession(interaction);
        }

        const embed = new EmbedBuilder()
            .setTitle('🔧 Form Application System Setup')
            .setDescription(
                '**Welcome to the Form Application System Setup Dashboard!**\n\n' +
                'This interactive dashboard will help you configure a comprehensive form application system with:\n' +
                '• Custom application questions\n' +
                '• Data verification against stored records\n' +
                '• Intelligent role assignment\n' +
                '• Automated processing workflows\n' +
                '• Comprehensive logging and review\n\n' +
                '**Current Configuration:**'
            )
            .setColor(0x3498DB)
            .addFields(
                {
                    name: '📝 Form Name',
                    value: session.formConfig.name || '*Not set*',
                    inline: true
                },
                {
                    name: '📋 Questions',
                    value: `${session.formConfig.questions.length} configured`,
                    inline: true
                },
                {
                    name: '🔍 Verification',
                    value: session.formConfig.verificationEnabled ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                },
                {
                    name: '🎯 Target Channels',
                    value: session.formConfig.targetChannels.length > 0 ? 
                        `${session.formConfig.targetChannels.length} selected` : '*None selected*',
                    inline: true
                },
                {
                    name: '📊 Log Channel',
                    value: session.formConfig.logChannel ? 
                        `<#${session.formConfig.logChannel}>` : '*Not set*',
                    inline: true
                },
                {
                    name: '🏷️ Role Rules',
                    value: `${session.formConfig.roleAssignmentRules.length} configured`,
                    inline: true
                }
            )
            .setFooter({ 
                text: `Setup Session • ${session.formConfig.isActive ? 'Active' : 'Inactive'} • Session expires in 30 minutes`
            })
            .setTimestamp();

        const components = this.createMainDashboardComponents(sessionId, session);

        if (isUpdate) {
            await interaction.update({ embeds: [embed], components });
        } else {
            await interaction.reply({ embeds: [embed], components, flags: MessageFlags.Ephemeral });
        }
    },

    /**
     * Create main dashboard components
     */
    createMainDashboardComponents(sessionId, session) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_basic_${sessionId}`)
                    .setLabel('Basic Settings')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⚙️'),
                new ButtonBuilder()
                    .setCustomId(`setup_questions_${sessionId}`)
                    .setLabel('Manage Questions')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId(`setup_verification_${sessionId}`)
                    .setLabel('Verification Setup')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔍')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_roles_${sessionId}`)
                    .setLabel('Role Assignment')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🏷️'),
                new ButtonBuilder()
                    .setCustomId(`setup_channels_${sessionId}`)
                    .setLabel('Channel Settings')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📺'),
                new ButtonBuilder()
                    .setCustomId(`setup_processing_${sessionId}`)
                    .setLabel('Processing Options')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚡')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_preview_${sessionId}`)
                    .setLabel('Preview Form')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('👁️'),
                new ButtonBuilder()
                    .setCustomId(`setup_save_${sessionId}`)
                    .setLabel('Save Configuration')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('💾')
                    .setDisabled(!this.isConfigurationValid(session.formConfig)),
                new ButtonBuilder()
                    .setCustomId(`setup_cancel_${sessionId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        return [row1, row2, row3];
    },

    /**
     * Check if configuration is valid for saving
     */
    isConfigurationValid(config) {
        return config.name && 
               config.name.trim() !== '' && 
               config.questions.length > 0 && 
               config.targetChannels.length > 0;
    },

    /**
     * Handle button interactions for setup dashboard
     */
    async handleSetupButton(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        
        if (parts.length < 3) {
            return await interaction.reply({
                content: '❌ Invalid button interaction',
                flags: MessageFlags.Ephemeral
            });
        }

        const action = parts[1];
        const sessionId = parts.slice(2).join('_');
        const session = setupSessions.get(sessionId);

        if (!session) {
            return await this.handleExpiredSession(interaction);
        }

        // Verify user permissions
        if (session.userId !== interaction.user.id) {
            return await interaction.reply({
                content: '❌ You can only interact with your own setup session',
                flags: MessageFlags.Ephemeral
            });
        }

        try {
            switch (action) {
                case 'basic':
                    await this.showBasicSettings(interaction, sessionId);
                    break;
                case 'questions':
                    await this.showQuestionManagement(interaction, sessionId);
                    break;
                case 'verification':
                    await this.showVerificationSettings(interaction, sessionId);
                    break;
                case 'roles':
                    await this.showRoleAssignment(interaction, sessionId);
                    break;
                case 'channels':
                    await this.showChannelSettings(interaction, sessionId);
                    break;
                case 'processing':
                    await this.showProcessingOptions(interaction, sessionId);
                    break;
                case 'preview':
                    await this.showFormPreview(interaction, sessionId);
                    break;
                case 'save':
                    await this.saveConfiguration(interaction, sessionId);
                    break;
                case 'cancel':
                    await this.cancelSetup(interaction, sessionId);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Unknown setup action',
                        flags: MessageFlags.Ephemeral
                    });
            }
        } catch (error) {
            console.error('[SETUP] Error handling button interaction:', error);
            await interaction.reply({
                content: `❌ **Error**\nFailed to process setup action: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Handle expired session
     */
    async handleExpiredSession(interaction) {
        const embed = new EmbedBuilder()
            .setTitle('⏰ Session Expired')
            .setDescription(
                'Your setup session has expired. Please run `/setup` again to start a new configuration session.'
            )
            .setColor(0xE74C3C);

        const restartButton = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_restart')
                    .setLabel('Start New Setup')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('🔄')
            );

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp({ embeds: [embed], components: [restartButton], flags: MessageFlags.Ephemeral });
        } else {
            await interaction.reply({ embeds: [embed], components: [restartButton], flags: MessageFlags.Ephemeral });
        }
    },

    /**
     * Save configuration to storage
     */
    async saveConfiguration(interaction, sessionId) {
        const session = setupSessions.get(sessionId);
        
        try {
            // Validate configuration
            FormConfigModels.validateFormConfig(session.formConfig);
            
            // Save to storage
            const configId = await formApplicationStorage.saveFormConfig(
                session.guildId, 
                session.formConfig
            );
            
            // Log the action
            await formApplicationStorage.addAuditLog({
                action: 'form_config_created',
                guildId: session.guildId,
                userId: session.userId,
                configId: configId,
                details: {
                    formName: session.formConfig.name,
                    questionCount: session.formConfig.questions.length,
                    verificationEnabled: session.formConfig.verificationEnabled
                }
            });
            
            // Clean up session
            setupSessions.delete(sessionId);
            
            const embed = new EmbedBuilder()
                .setTitle('✅ Configuration Saved Successfully!')
                .setDescription(
                    `**Form "${session.formConfig.name}" has been created and saved.**\n\n` +
                    `**Configuration ID:** \`${configId}\`\n` +
                    `**Questions:** ${session.formConfig.questions.length}\n` +
                    `**Verification:** ${session.formConfig.verificationEnabled ? 'Enabled' : 'Disabled'}\n` +
                    `**Target Channels:** ${session.formConfig.targetChannels.length}\n` +
                    `**Role Rules:** ${session.formConfig.roleAssignmentRules.length}\n\n` +
                    `Users can now submit applications using this form configuration. ` +
                    `Use \`/form-admin\` to manage applications and review submissions.`
                )
                .setColor(0x27AE60)
                .setFooter({ text: 'Form Application System' })
                .setTimestamp();

            await interaction.update({ embeds: [embed], components: [] });
            
        } catch (error) {
            console.error('[SETUP] Error saving configuration:', error);
            await interaction.reply({
                content: `❌ **Error Saving Configuration**\n${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Cancel setup and clean up session
     */
    async cancelSetup(interaction, sessionId) {
        setupSessions.delete(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('❌ Setup Cancelled')
            .setDescription('Form application system setup has been cancelled. No changes were saved.')
            .setColor(0xE74C3C);

        await interaction.update({ embeds: [embed], components: [] });
    },

    // ==================== CONFIGURATION SCREENS ====================

    /**
     * Show basic settings configuration
     */
    async showBasicSettings(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('⚙️ Basic Settings')
            .setDescription(
                'Configure the fundamental settings for your form application system.\n\n' +
                '**Current Settings:**'
            )
            .setColor(0x3498DB)
            .addFields(
                {
                    name: '📝 Form Name',
                    value: session.formConfig.name || '*Not set*',
                    inline: true
                },
                {
                    name: '📄 Description',
                    value: session.formConfig.description || '*Not set*',
                    inline: true
                },
                {
                    name: '🔄 Status',
                    value: session.formConfig.isActive ? '✅ Active' : '❌ Inactive',
                    inline: true
                },
                {
                    name: '⚡ Auto Approve',
                    value: session.formConfig.autoApprove ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                },
                {
                    name: '👥 Admin Review Required',
                    value: session.formConfig.requireAdminReview ? '✅ Yes' : '❌ No',
                    inline: true
                },
                {
                    name: '🔄 Allow Resubmission',
                    value: session.formConfig.allowResubmission ? '✅ Yes' : '❌ No',
                    inline: true
                }
            )
            .setFooter({ text: 'Use the buttons below to modify these settings' });

        const components = this.createBasicSettingsComponents(sessionId);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create basic settings components
     */
    createBasicSettingsComponents(sessionId) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_edit_name_${sessionId}`)
                    .setLabel('Edit Name')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📝'),
                new ButtonBuilder()
                    .setCustomId(`setup_edit_description_${sessionId}`)
                    .setLabel('Edit Description')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📄'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_active_${sessionId}`)
                    .setLabel('Toggle Active')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔄')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_auto_approve_${sessionId}`)
                    .setLabel('Toggle Auto Approve')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚡'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_admin_review_${sessionId}`)
                    .setLabel('Toggle Admin Review')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('👥'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_resubmission_${sessionId}`)
                    .setLabel('Toggle Resubmission')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔄')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1, row2, row3];
    },

    /**
     * Show question management screen
     */
    async showQuestionManagement(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('📝 Question Management')
            .setDescription(
                'Manage the questions that will appear in your application form.\n\n' +
                '**Question Types Available:**\n' +
                '• Text Input - Simple text responses\n' +
                '• Email - Email address validation\n' +
                '• Phone - Phone number validation\n' +
                '• Date - Date picker\n' +
                '• Number - Numeric input\n' +
                '• Dropdown - Single selection from options\n' +
                '• Multiple Choice - Single selection with radio buttons\n' +
                '• Checkbox - Multiple selections\n' +
                '• Text Area - Long text responses\n\n' +
                `**Current Questions (${session.formConfig.questions.length}):**`
            )
            .setColor(0x9B59B6);

        // Add fields for existing questions
        if (session.formConfig.questions.length > 0) {
            session.formConfig.questions
                .sort((a, b) => a.order - b.order)
                .slice(0, 10) // Limit to first 10 questions for display
                .forEach((question, index) => {
                    embed.addFields({
                        name: `${index + 1}. ${question.label}`,
                        value: `Type: ${question.type}${question.required ? ' (Required)' : ''}${question.isVerificationCriteria ? ' 🔍' : ''}`,
                        inline: false
                    });
                });

            if (session.formConfig.questions.length > 10) {
                embed.addFields({
                    name: '...',
                    value: `And ${session.formConfig.questions.length - 10} more questions`,
                    inline: false
                });
            }
        } else {
            embed.addFields({
                name: 'No Questions',
                value: 'Click "Add Question" to create your first question.',
                inline: false
            });
        }

        const components = this.createQuestionManagementComponents(sessionId, session);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create question management components
     */
    createQuestionManagementComponents(sessionId, session) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_add_question_${sessionId}`)
                    .setLabel('Add Question')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId(`setup_edit_question_${sessionId}`)
                    .setLabel('Edit Question')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('✏️')
                    .setDisabled(session.formConfig.questions.length === 0),
                new ButtonBuilder()
                    .setCustomId(`setup_delete_question_${sessionId}`)
                    .setLabel('Delete Question')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🗑️')
                    .setDisabled(session.formConfig.questions.length === 0)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_reorder_questions_${sessionId}`)
                    .setLabel('Reorder Questions')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔄')
                    .setDisabled(session.formConfig.questions.length < 2),
                new ButtonBuilder()
                    .setCustomId(`setup_import_questions_${sessionId}`)
                    .setLabel('Import Questions')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📥'),
                new ButtonBuilder()
                    .setCustomId(`setup_export_questions_${sessionId}`)
                    .setLabel('Export Questions')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📤')
                    .setDisabled(session.formConfig.questions.length === 0)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1, row2, row3];
    },

    /**
     * Show verification settings screen
     */
    async showVerificationSettings(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const verificationQuestions = session.formConfig.questions.filter(q => q.isVerificationCriteria);

        const embed = new EmbedBuilder()
            .setTitle('🔍 Verification Settings')
            .setDescription(
                'Configure data verification to automatically check form responses against stored data.\n\n' +
                '**How Verification Works:**\n' +
                '• Mark questions as "verification criteria"\n' +
                '• Bot compares answers against data in specified channels\n' +
                '• Uses configurable similarity thresholds (70% default, 85-90% strict)\n' +
                '• Supports multiple matching algorithms\n' +
                '• Automatic approval for verified applications\n\n' +
                '**Current Settings:**'
            )
            .setColor(0xE67E22)
            .addFields(
                {
                    name: '🔍 Verification Status',
                    value: session.formConfig.verificationEnabled ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                },
                {
                    name: '📊 Similarity Threshold',
                    value: `${session.formConfig.similarityThreshold}%`,
                    inline: true
                },
                {
                    name: '🎯 Strict Matching',
                    value: session.formConfig.strictMatching ? '✅ Enabled (85-90%)' : '❌ Disabled',
                    inline: true
                },
                {
                    name: '📺 Verification Channels',
                    value: session.formConfig.verificationChannels.length > 0 ?
                        session.formConfig.verificationChannels.map(id => `<#${id}>`).join(', ') :
                        '*None selected*',
                    inline: false
                },
                {
                    name: '❓ Verification Questions',
                    value: verificationQuestions.length > 0 ?
                        verificationQuestions.map(q => `• ${q.label}`).join('\n') :
                        '*No questions marked for verification*',
                    inline: false
                }
            );

        const components = this.createVerificationSettingsComponents(sessionId, session);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create verification settings components
     */
    createVerificationSettingsComponents(sessionId, session) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_verification_${sessionId}`)
                    .setLabel('Toggle Verification')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔍'),
                new ButtonBuilder()
                    .setCustomId(`setup_set_threshold_${sessionId}`)
                    .setLabel('Set Threshold')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_strict_${sessionId}`)
                    .setLabel('Toggle Strict Mode')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎯')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_select_verification_channels_${sessionId}`)
                    .setLabel('Select Verification Channels')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📺'),
                new ButtonBuilder()
                    .setCustomId(`setup_manage_verification_questions_${sessionId}`)
                    .setLabel('Manage Verification Questions')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❓')
                    .setDisabled(session.formConfig.questions.length === 0)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_test_verification_${sessionId}`)
                    .setLabel('Test Verification')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🧪')
                    .setDisabled(!session.formConfig.verificationEnabled || session.formConfig.verificationChannels.length === 0),
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1, row2, row3];
    },

    /**
     * Show role assignment configuration
     */
    async showRoleAssignment(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('🏷️ Role Assignment Configuration')
            .setDescription(
                'Configure automatic role assignment based on form responses.\n\n' +
                '**Assignment Rules:**\n' +
                '• Create rules based on specific answers\n' +
                '• Support multiple conditions per rule\n' +
                '• Assign different roles for different answer combinations\n' +
                '• Set priority for conflicting rules\n' +
                '• Configure default role for unmatched applications\n\n' +
                `**Current Configuration:**`
            )
            .setColor(0xF39C12)
            .addFields(
                {
                    name: '📋 Assignment Rules',
                    value: session.formConfig.roleAssignmentRules.length > 0 ?
                        `${session.formConfig.roleAssignmentRules.length} rules configured` :
                        '*No rules configured*',
                    inline: true
                },
                {
                    name: '🎯 Default Role',
                    value: session.formConfig.defaultRole ?
                        `<@&${session.formConfig.defaultRole}>` :
                        '*Not set*',
                    inline: true
                },
                {
                    name: '🔢 Max Roles Per User',
                    value: session.formConfig.maxRolesPerUser.toString(),
                    inline: true
                }
            );

        // Add details for existing rules
        if (session.formConfig.roleAssignmentRules.length > 0) {
            const ruleDetails = session.formConfig.roleAssignmentRules
                .slice(0, 5) // Show first 5 rules
                .map((rule, index) => {
                    const question = session.formConfig.questions.find(q => q.id === rule.questionId);
                    return `${index + 1}. **${rule.name}**\n` +
                           `   Question: ${question?.label || 'Unknown'}\n` +
                           `   Condition: ${rule.condition}\n` +
                           `   Role: ${rule.roleName || 'Unknown'}`;
                }).join('\n\n');

            embed.addFields({
                name: 'Rule Details',
                value: ruleDetails,
                inline: false
            });

            if (session.formConfig.roleAssignmentRules.length > 5) {
                embed.addFields({
                    name: '...',
                    value: `And ${session.formConfig.roleAssignmentRules.length - 5} more rules`,
                    inline: false
                });
            }
        }

        const components = this.createRoleAssignmentComponents(sessionId, session);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create role assignment components
     */
    createRoleAssignmentComponents(sessionId, session) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_add_role_rule_${sessionId}`)
                    .setLabel('Add Role Rule')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('➕')
                    .setDisabled(session.formConfig.questions.length === 0),
                new ButtonBuilder()
                    .setCustomId(`setup_edit_role_rule_${sessionId}`)
                    .setLabel('Edit Role Rule')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('✏️')
                    .setDisabled(session.formConfig.roleAssignmentRules.length === 0),
                new ButtonBuilder()
                    .setCustomId(`setup_delete_role_rule_${sessionId}`)
                    .setLabel('Delete Role Rule')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🗑️')
                    .setDisabled(session.formConfig.roleAssignmentRules.length === 0)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_set_default_role_${sessionId}`)
                    .setLabel('Set Default Role')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎯'),
                new ButtonBuilder()
                    .setCustomId(`setup_set_max_roles_${sessionId}`)
                    .setLabel('Set Max Roles')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔢'),
                new ButtonBuilder()
                    .setCustomId(`setup_test_role_assignment_${sessionId}`)
                    .setLabel('Test Assignment')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🧪')
                    .setDisabled(session.formConfig.roleAssignmentRules.length === 0)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1, row2, row3];
    },

    /**
     * Show channel settings configuration
     */
    async showChannelSettings(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('📺 Channel Settings')
            .setDescription(
                'Configure where forms can be submitted and where logs are sent.\n\n' +
                '**Channel Types:**\n' +
                '• **Target Channels** - Where users can submit applications\n' +
                '• **Log Channel** - Where application logs and admin notifications are sent\n' +
                '• **Verification Channels** - Where verification data is stored\n\n' +
                '**Current Settings:**'
            )
            .setColor(0x8E44AD)
            .addFields(
                {
                    name: '🎯 Target Channels',
                    value: session.formConfig.targetChannels.length > 0 ?
                        session.formConfig.targetChannels.map(id => `<#${id}>`).join('\n') :
                        '*None selected*',
                    inline: true
                },
                {
                    name: '📊 Log Channel',
                    value: session.formConfig.logChannel ?
                        `<#${session.formConfig.logChannel}>` :
                        '*Not set*',
                    inline: true
                },
                {
                    name: '🔍 Verification Channels',
                    value: session.formConfig.verificationChannels.length > 0 ?
                        session.formConfig.verificationChannels.map(id => `<#${id}>`).join('\n') :
                        '*None selected*',
                    inline: true
                }
            );

        const components = this.createChannelSettingsComponents(sessionId);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create channel settings components
     */
    createChannelSettingsComponents(sessionId) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_select_target_channels_${sessionId}`)
                    .setLabel('Select Target Channels')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🎯'),
                new ButtonBuilder()
                    .setCustomId(`setup_select_log_channel_${sessionId}`)
                    .setLabel('Select Log Channel')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📊'),
                new ButtonBuilder()
                    .setCustomId(`setup_select_verification_channels_${sessionId}`)
                    .setLabel('Select Verification Channels')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔍')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1, row2];
    },

    /**
     * Show processing options configuration
     */
    async showProcessingOptions(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('⚡ Processing Options')
            .setDescription(
                'Configure how applications are processed and what notifications are sent.\n\n' +
                '**Processing Settings:**'
            )
            .setColor(0x16A085)
            .addFields(
                {
                    name: '⚡ Auto Approve',
                    value: session.formConfig.autoApprove ?
                        '✅ Enabled - Applications passing verification are auto-approved' :
                        '❌ Disabled - All applications require manual review',
                    inline: false
                },
                {
                    name: '👥 Admin Review Required',
                    value: session.formConfig.requireAdminReview ?
                        '✅ Yes - Admins must review all applications' :
                        '❌ No - Applications can be processed automatically',
                    inline: false
                },
                {
                    name: '🔄 Allow Resubmission',
                    value: session.formConfig.allowResubmission ?
                        '✅ Yes - Users can resubmit if rejected' :
                        '❌ No - One submission per user',
                    inline: false
                },
                {
                    name: '📧 DM on Approval',
                    value: session.formConfig.sendDMOnApproval ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                },
                {
                    name: '📧 DM on Rejection',
                    value: session.formConfig.sendDMOnRejection ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                },
                {
                    name: '✅ Approval Message',
                    value: `"${session.formConfig.approvalMessage}"`,
                    inline: false
                },
                {
                    name: '❌ Rejection Message',
                    value: `"${session.formConfig.rejectionMessage}"`,
                    inline: false
                }
            );

        const components = this.createProcessingOptionsComponents(sessionId);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create processing options components
     */
    createProcessingOptionsComponents(sessionId) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_auto_approve_${sessionId}`)
                    .setLabel('Toggle Auto Approve')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚡'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_admin_review_${sessionId}`)
                    .setLabel('Toggle Admin Review')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('👥'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_resubmission_${sessionId}`)
                    .setLabel('Toggle Resubmission')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔄')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_dm_approval_${sessionId}`)
                    .setLabel('Toggle DM on Approval')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📧'),
                new ButtonBuilder()
                    .setCustomId(`setup_toggle_dm_rejection_${sessionId}`)
                    .setLabel('Toggle DM on Rejection')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📧')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_edit_approval_message_${sessionId}`)
                    .setLabel('Edit Approval Message')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('✅'),
                new ButtonBuilder()
                    .setCustomId(`setup_edit_rejection_message_${sessionId}`)
                    .setLabel('Edit Rejection Message')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❌'),
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1, row2, row3];
    },

    /**
     * Show form preview
     */
    async showFormPreview(interaction, sessionId) {
        const session = setupSessions.get(sessionId);

        const embed = new EmbedBuilder()
            .setTitle('👁️ Form Preview')
            .setDescription(
                `**Preview of "${session.formConfig.name}"**\n\n` +
                (session.formConfig.description ? `${session.formConfig.description}\n\n` : '') +
                '**Questions:**'
            )
            .setColor(0x27AE60);

        if (session.formConfig.questions.length > 0) {
            session.formConfig.questions
                .sort((a, b) => a.order - b.order)
                .forEach((question, index) => {
                    let questionText = `**${index + 1}. ${question.label}**`;
                    if (question.required) questionText += ' *';
                    if (question.isVerificationCriteria) questionText += ' 🔍';

                    let questionDetails = `Type: ${question.type}`;
                    if (question.description) questionDetails += `\nDescription: ${question.description}`;
                    if (question.placeholder) questionDetails += `\nPlaceholder: ${question.placeholder}`;
                    if (question.options && question.options.length > 0) {
                        questionDetails += `\nOptions: ${question.options.map(opt => opt.label || opt.value).join(', ')}`;
                    }

                    embed.addFields({
                        name: questionText,
                        value: questionDetails,
                        inline: false
                    });
                });
        } else {
            embed.addFields({
                name: 'No Questions',
                value: 'No questions have been configured yet.',
                inline: false
            });
        }

        embed.addFields({
            name: 'Configuration Summary',
            value: `• **Questions:** ${session.formConfig.questions.length}\n` +
                   `• **Verification:** ${session.formConfig.verificationEnabled ? 'Enabled' : 'Disabled'}\n` +
                   `• **Target Channels:** ${session.formConfig.targetChannels.length}\n` +
                   `• **Role Rules:** ${session.formConfig.roleAssignmentRules.length}\n` +
                   `• **Auto Approve:** ${session.formConfig.autoApprove ? 'Yes' : 'No'}\n` +
                   `• **Admin Review:** ${session.formConfig.requireAdminReview ? 'Required' : 'Optional'}`,
            inline: false
        });

        const components = this.createFormPreviewComponents(sessionId, session);
        await interaction.update({ embeds: [embed], components });
    },

    /**
     * Create form preview components
     */
    createFormPreviewComponents(sessionId, session) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`setup_test_form_${sessionId}`)
                    .setLabel('Test Form Submission')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('🧪')
                    .setDisabled(session.formConfig.questions.length === 0),
                new ButtonBuilder()
                    .setCustomId(`setup_export_config_${sessionId}`)
                    .setLabel('Export Configuration')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📤'),
                new ButtonBuilder()
                    .setCustomId(`setup_back_main_${sessionId}`)
                    .setLabel('Back to Main')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️')
            );

        return [row1];
    }
};

// Export setup sessions for use by other modules
module.exports.setupSessions = setupSessions;
