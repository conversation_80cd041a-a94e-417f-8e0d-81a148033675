/**
 * Interaction Test
 * Tests the application-setup command interaction handling
 */

const setupCommand = require('../commands/setup.js');

class InteractionTest {
    constructor() {
        this.testResults = [];
    }

    /**
     * Run interaction tests
     */
    async runTests() {
        console.log('🧪 Testing Application-Setup Command Interactions...\n');

        try {
            await this.testCommandExecution();
            await this.testButtonInteractions();
            await this.testModalInteractions();

            this.printTestResults();

        } catch (error) {
            console.error('❌ Interaction test suite failed:', error);
        }
    }

    /**
     * Test command execution
     */
    async testCommandExecution() {
        console.log('⚡ Testing Command Execution...');

        try {
            // Mock interaction object
            const mockInteraction = {
                user: { id: 'test_user_123' },
                guild: { id: 'test_guild_456' },
                reply: async (options) => {
                    console.log('  📤 Reply sent:', options.embeds?.[0]?.title || 'No embed title');
                    return { id: 'mock_message_id' };
                },
                update: async (options) => {
                    console.log('  🔄 Update sent:', options.embeds?.[0]?.title || 'No embed title');
                    return { id: 'mock_message_id' };
                }
            };

            // Test command execution
            await setupCommand.execute(mockInteraction);

            this.addTestResult('Command Execution', true, 'Command executed without errors');

        } catch (error) {
            this.addTestResult('Command Execution', false, error.message);
        }
    }

    /**
     * Test button interactions
     */
    async testButtonInteractions() {
        console.log('🔘 Testing Button Interactions...');

        try {
            // Create a test session first
            const setupSessions = setupCommand.setupSessions;
            const sessionId = 'test_user_123_1753042146257';
            const session = {
                userId: 'test_user_123',
                guildId: 'test_guild_456',
                createdAt: Date.now(),
                currentStep: 'main',
                formConfig: {
                    name: 'Test Form',
                    description: 'Test Description',
                    isActive: true,
                    autoApprove: false,
                    requireAdminReview: false,
                    allowResubmission: true,
                    questions: [],
                    roleAssignmentRules: []
                }
            };
            setupSessions.set(sessionId, session);

            // Mock button interaction
            const mockButtonInteraction = {
                customId: `setup_basic_${sessionId}`,
                user: { id: 'test_user_123' },
                guild: { id: 'test_guild_456' },
                update: async (options) => {
                    console.log('  🔄 Button update sent:', options.embeds?.[0]?.title || 'No embed title');
                    return { id: 'mock_message_id' };
                },
                reply: async (options) => {
                    console.log('  📤 Button reply sent:', options.content || 'No content');
                    return { id: 'mock_message_id' };
                }
            };

            // Test button handling
            await setupCommand.handleSetupButton(mockButtonInteraction);

            this.addTestResult('Button Interactions', true, 'Button interactions handled without errors');

        } catch (error) {
            this.addTestResult('Button Interactions', false, error.message);
        }
    }

    /**
     * Test modal interactions
     */
    async testModalInteractions() {
        console.log('📝 Testing Modal Interactions...');

        try {
            // Create a test session
            const setupSessions = setupCommand.setupSessions;
            const sessionId = 'test_user_123_1753042146257';
            
            // Mock modal interaction
            const mockModalInteraction = {
                customId: `setup_modal_name_${sessionId}`,
                user: { id: 'test_user_123' },
                guild: { id: 'test_guild_456' },
                fields: {
                    getTextInputValue: (fieldId) => {
                        if (fieldId === 'form_name') return 'Updated Test Form';
                        if (fieldId === 'form_description') return 'Updated Test Description';
                        return '';
                    }
                },
                reply: async (options) => {
                    console.log('  📤 Modal reply sent:', options.content || 'No content');
                    return { id: 'mock_message_id' };
                },
                followUp: async (options) => {
                    console.log('  📤 Modal followUp sent:', options.content || 'No content');
                    return { id: 'mock_message_id' };
                }
            };

            // Test modal handling
            await setupCommand.handleSetupModal(mockModalInteraction);

            this.addTestResult('Modal Interactions', true, 'Modal interactions handled without errors');

        } catch (error) {
            this.addTestResult('Modal Interactions', false, error.message);
        }
    }

    /**
     * Add test result
     */
    addTestResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed,
            message
        });

        const status = passed ? '✅' : '❌';
        console.log(`  ${status} ${testName}: ${message}`);
    }

    /**
     * Print test results summary
     */
    printTestResults() {
        console.log('\n📊 Interaction Test Results:');
        console.log('============================');

        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        const failed = total - passed;

        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${failed}`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

        if (failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(r => !r.passed)
                .forEach(r => console.log(`  • ${r.name}: ${r.message}`));
        }

        console.log('\n🎉 Interaction test completed!');
        
        if (passed === total) {
            console.log('\n✅ SUCCESS: All interaction tests passed!');
            console.log('\n🔧 Interaction Features Verified:');
            console.log('  • Command execution and response');
            console.log('  • Button interaction handling');
            console.log('  • Modal interaction processing');
            console.log('\n🚀 The interaction system is working correctly!');
        } else {
            console.log('\n⚠️  Some tests failed. Please review the issues above.');
        }
    }
}

// Export for use
module.exports = InteractionTest;

// Run tests if this file is executed directly
if (require.main === module) {
    const testSuite = new InteractionTest();
    testSuite.runTests().catch(console.error);
}
