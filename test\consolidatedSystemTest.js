/**
 * Consolidated System Test
 * Tests the unified application-setup command with all integrated functionality
 */

const { FormConfigModels, QUESTION_TYPES, APPLICATION_STATUS } = require('../utils/formConfigModels');
const formApplicationStorage = require('../utils/formApplicationStorage');

class ConsolidatedSystemTest {
    constructor() {
        this.testResults = [];
        this.testGuildId = 'test_guild_consolidated';
        this.testUserId = 'test_user_consolidated';
    }

    /**
     * Run all consolidated system tests
     */
    async runAllTests() {
        console.log('🧪 Starting Consolidated Application System Test Suite...\n');

        try {
            await this.testCommandStructure();
            await this.testIntegratedFunctionality();
            await this.testDataConsistency();
            await this.testUserWorkflows();

            this.printTestResults();

        } catch (error) {
            console.error('❌ Consolidated test suite failed:', error);
        }
    }

    /**
     * Test command structure and loading
     */
    async testCommandStructure() {
        console.log('🔧 Testing Command Structure...');

        try {
            // Test that the setup command is properly renamed
            const setupCommand = require('../commands/setup.js');
            this.assert(setupCommand.data.name === 'application-setup', 'Command should be renamed to application-setup');

            // Test that old commands are removed
            try {
                require('../commands/form.js');
                this.addTestResult('Command Structure', false, 'Old form.js command still exists');
                return;
            } catch (error) {
                // Expected - file should not exist
            }

            try {
                require('../commands/form-admin.js');
                this.addTestResult('Command Structure', false, 'Old form-admin.js command still exists');
                return;
            } catch (error) {
                // Expected - file should not exist
            }

            // Test that all handler functions exist
            this.assert(typeof setupCommand.handleSetupButton === 'function', 'handleSetupButton should exist');
            this.assert(typeof setupCommand.handleFormSelection === 'function', 'handleFormSelection should exist');

            this.addTestResult('Command Structure', true, 'All command structure tests passed');

        } catch (error) {
            this.addTestResult('Command Structure', false, error.message);
        }
    }

    /**
     * Test integrated functionality
     */
    async testIntegratedFunctionality() {
        console.log('⚡ Testing Integrated Functionality...');

        try {
            // Initialize storage
            await formApplicationStorage.initializeStorage();

            // Test form creation through setup
            const testConfig = FormConfigModels.createFormConfig({
                guildId: this.testGuildId,
                name: 'Consolidated Test Form',
                description: 'Testing the consolidated system',
                questions: [
                    FormConfigModels.createQuestion({
                        type: QUESTION_TYPES.TEXT,
                        label: 'Test Question',
                        required: true
                    })
                ]
            });

            const configId = await formApplicationStorage.saveFormConfig(this.testGuildId, testConfig);
            this.assert(configId, 'Form config should be saved successfully');

            // Test application creation
            const testApplication = FormConfigModels.createApplication({
                configId: configId,
                guildId: this.testGuildId,
                userId: this.testUserId,
                username: 'ConsolidatedTestUser',
                answers: { 'test_question': 'test_answer' }
            });

            const applicationId = await formApplicationStorage.saveApplication(testApplication);
            this.assert(applicationId, 'Application should be saved successfully');

            // Test statistics functionality
            const applications = await formApplicationStorage.getApplicationsByGuild(this.testGuildId);
            this.assert(applications.length > 0, 'Should retrieve saved applications');

            // Test audit logging
            await formApplicationStorage.addAuditLog({
                action: 'consolidated_test',
                guildId: this.testGuildId,
                userId: this.testUserId,
                details: { test: 'consolidated_functionality' }
            });

            const auditLogs = await formApplicationStorage.getAuditLogs({ guildId: this.testGuildId, limit: 1 });
            this.assert(auditLogs.length > 0, 'Audit log should be created');

            this.addTestResult('Integrated Functionality', true, 'All integration tests passed');

        } catch (error) {
            this.addTestResult('Integrated Functionality', false, error.message);
        }
    }

    /**
     * Test data consistency across the consolidated system
     */
    async testDataConsistency() {
        console.log('📊 Testing Data Consistency...');

        try {
            // Test that all data is accessible through the unified interface
            const guildConfigs = await formApplicationStorage.getGuildFormConfigs(this.testGuildId);
            this.assert(Object.keys(guildConfigs).length > 0, 'Should have guild configurations');

            const applications = await formApplicationStorage.getApplicationsByGuild(this.testGuildId);
            this.assert(applications.length > 0, 'Should have applications');

            // Test storage statistics
            const storageStats = await formApplicationStorage.getStorageStats();
            this.assert(storageStats.applications > 0, 'Storage stats should show applications');
            this.assert(storageStats.formConfigs > 0, 'Storage stats should show form configs');

            // Test data integrity
            const firstApp = applications[0];
            const relatedConfig = guildConfigs[firstApp.configId];
            this.assert(relatedConfig, 'Application should have related config');
            this.assert(relatedConfig.guildId === firstApp.guildId, 'Guild IDs should match');

            this.addTestResult('Data Consistency', true, 'All data consistency tests passed');

        } catch (error) {
            this.addTestResult('Data Consistency', false, error.message);
        }
    }

    /**
     * Test user workflows through the consolidated system
     */
    async testUserWorkflows() {
        console.log('👤 Testing User Workflows...');

        try {
            // Test form submission workflow
            const guildConfigs = await formApplicationStorage.getGuildFormConfigs(this.testGuildId);
            const testConfig = Object.values(guildConfigs)[0];
            
            this.assert(testConfig, 'Should have a test configuration');
            this.assert(testConfig.questions.length > 0, 'Config should have questions');

            // Test application status workflow
            const applications = await formApplicationStorage.getApplicationsByGuild(this.testGuildId);
            const testApp = applications[0];

            // Update application status (simulating admin review)
            await formApplicationStorage.updateApplication(testApp.id, {
                status: APPLICATION_STATUS.APPROVED,
                reviewedBy: this.testUserId,
                reviewNotes: 'Consolidated test approval',
                processedAt: Date.now()
            });

            // Verify status update
            const updatedApp = await formApplicationStorage.getApplication(testApp.id);
            this.assert(updatedApp.status === APPLICATION_STATUS.APPROVED, 'Application status should be updated');
            this.assert(updatedApp.reviewedBy === this.testUserId, 'Reviewer should be recorded');

            // Test filtering functionality
            const approvedApps = await formApplicationStorage.getApplicationsByGuild(this.testGuildId, {
                status: APPLICATION_STATUS.APPROVED
            });
            this.assert(approvedApps.length > 0, 'Should find approved applications');

            this.addTestResult('User Workflows', true, 'All workflow tests passed');

        } catch (error) {
            this.addTestResult('User Workflows', false, error.message);
        }
    }

    /**
     * Add test result
     */
    addTestResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed,
            message
        });

        const status = passed ? '✅' : '❌';
        console.log(`  ${status} ${testName}: ${message}`);
    }

    /**
     * Assert condition
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(`Assertion failed: ${message}`);
        }
    }

    /**
     * Print test results summary
     */
    printTestResults() {
        console.log('\n📊 Consolidated System Test Results:');
        console.log('====================================');

        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        const failed = total - passed;

        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${failed}`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

        if (failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(r => !r.passed)
                .forEach(r => console.log(`  • ${r.name}: ${r.message}`));
        }

        console.log('\n🎉 Consolidated system test completed!');
        
        if (passed === total) {
            console.log('\n✅ SUCCESS: All tests passed! The consolidated application system is working correctly.');
            console.log('\n📋 Available functionality through /application-setup:');
            console.log('  • Form configuration and management');
            console.log('  • User application submission');
            console.log('  • Admin application review');
            console.log('  • Statistics and analytics');
            console.log('  • Data export capabilities');
            console.log('  • System cleanup and maintenance');
            console.log('\n🚀 The unified command system is ready for production use!');
        } else {
            console.log('\n⚠️  Some tests failed. Please review the issues above.');
        }
    }
}

// Export for use
module.exports = ConsolidatedSystemTest;

// Run tests if this file is executed directly
if (require.main === module) {
    const testSuite = new ConsolidatedSystemTest();
    testSuite.runAllTests().catch(console.error);
}
