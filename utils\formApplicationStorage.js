/**
 * Form Application Storage System
 * Handles persistent storage for form configurations, applications, and verification data
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class FormApplicationStorage {
    constructor() {
        this.dataDir = path.join(__dirname, '..', 'data');
        this.configFile = path.join(this.dataDir, 'form_configs.json');
        this.applicationsFile = path.join(this.dataDir, 'applications.json');
        this.verificationDataFile = path.join(this.dataDir, 'verification_data.json');
        this.auditLogFile = path.join(this.dataDir, 'audit_log.json');
        
        // In-memory caches for performance
        this.configCache = new Map();
        this.applicationCache = new Map();
        this.verificationCache = new Map();
        
        this.isWriting = false;
        this.writeQueue = [];
        
        this.initializeStorage();
    }

    /**
     * Initialize storage directories and files
     */
    async initializeStorage() {
        try {
            // Create data directory if it doesn't exist
            await fs.mkdir(this.dataDir, { recursive: true });
            
            // Initialize files with default structures if they don't exist
            await this.initializeFile(this.configFile, { guilds: {} });
            await this.initializeFile(this.applicationsFile, { applications: {} });
            await this.initializeFile(this.verificationDataFile, { guilds: {} });
            await this.initializeFile(this.auditLogFile, { logs: [] });
            
            console.log('[FORM_STORAGE] Storage system initialized successfully');
        } catch (error) {
            console.error('[FORM_STORAGE] Error initializing storage:', error);
            throw error;
        }
    }

    /**
     * Initialize a file with default content if it doesn't exist
     */
    async initializeFile(filePath, defaultContent) {
        try {
            await fs.access(filePath);
        } catch (error) {
            if (error.code === 'ENOENT') {
                await fs.writeFile(filePath, JSON.stringify(defaultContent, null, 2));
                console.log(`[FORM_STORAGE] Created ${path.basename(filePath)}`);
            } else {
                throw error;
            }
        }
    }

    /**
     * Read data from a file with error handling
     */
    async readFile(filePath) {
        try {
            const data = await fs.readFile(filePath, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            console.error(`[FORM_STORAGE] Error reading ${path.basename(filePath)}:`, error);
            throw error;
        }
    }

    /**
     * Write data to a file with queue management
     */
    async writeFile(filePath, data) {
        return new Promise((resolve, reject) => {
            this.writeQueue.push({ filePath, data, resolve, reject });
            this.processWriteQueue();
        });
    }

    /**
     * Process the write queue to prevent concurrent writes
     */
    async processWriteQueue() {
        if (this.isWriting || this.writeQueue.length === 0) return;
        
        this.isWriting = true;
        
        while (this.writeQueue.length > 0) {
            const { filePath, data, resolve, reject } = this.writeQueue.shift();
            
            try {
                await fs.writeFile(filePath, JSON.stringify(data, null, 2));
                resolve();
            } catch (error) {
                console.error(`[FORM_STORAGE] Error writing ${path.basename(filePath)}:`, error);
                reject(error);
            }
        }
        
        this.isWriting = false;
    }

    // ==================== FORM CONFIGURATION METHODS ====================

    /**
     * Save form configuration for a guild
     */
    async saveFormConfig(guildId, config) {
        try {
            const data = await this.readFile(this.configFile);
            
            if (!data.guilds[guildId]) {
                data.guilds[guildId] = {};
            }
            
            const configId = config.id || uuidv4();
            config.id = configId;
            config.updatedAt = Date.now();
            
            data.guilds[guildId][configId] = config;
            
            await this.writeFile(this.configFile, data);
            this.configCache.set(`${guildId}_${configId}`, config);
            
            console.log(`[FORM_STORAGE] Saved form config ${configId} for guild ${guildId}`);
            return configId;
        } catch (error) {
            console.error('[FORM_STORAGE] Error saving form config:', error);
            throw error;
        }
    }

    /**
     * Get form configuration by ID
     */
    async getFormConfig(guildId, configId) {
        try {
            const cacheKey = `${guildId}_${configId}`;
            if (this.configCache.has(cacheKey)) {
                return this.configCache.get(cacheKey);
            }
            
            const data = await this.readFile(this.configFile);
            const config = data.guilds[guildId]?.[configId];
            
            if (config) {
                this.configCache.set(cacheKey, config);
            }
            
            return config || null;
        } catch (error) {
            console.error('[FORM_STORAGE] Error getting form config:', error);
            throw error;
        }
    }

    /**
     * Get all form configurations for a guild
     */
    async getGuildFormConfigs(guildId) {
        try {
            const data = await this.readFile(this.configFile);
            return data.guilds[guildId] || {};
        } catch (error) {
            console.error('[FORM_STORAGE] Error getting guild form configs:', error);
            throw error;
        }
    }

    /**
     * Delete form configuration
     */
    async deleteFormConfig(guildId, configId) {
        try {
            const data = await this.readFile(this.configFile);
            
            if (data.guilds[guildId]?.[configId]) {
                delete data.guilds[guildId][configId];
                await this.writeFile(this.configFile, data);
                this.configCache.delete(`${guildId}_${configId}`);
                
                console.log(`[FORM_STORAGE] Deleted form config ${configId} for guild ${guildId}`);
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('[FORM_STORAGE] Error deleting form config:', error);
            throw error;
        }
    }

    // ==================== APPLICATION METHODS ====================

    /**
     * Save application submission
     */
    async saveApplication(application) {
        try {
            const data = await this.readFile(this.applicationsFile);
            
            const applicationId = application.id || uuidv4();
            application.id = applicationId;
            application.submittedAt = application.submittedAt || Date.now();
            application.status = application.status || 'pending';
            
            data.applications[applicationId] = application;
            
            await this.writeFile(this.applicationsFile, data);
            this.applicationCache.set(applicationId, application);
            
            console.log(`[FORM_STORAGE] Saved application ${applicationId}`);
            return applicationId;
        } catch (error) {
            console.error('[FORM_STORAGE] Error saving application:', error);
            throw error;
        }
    }

    /**
     * Get application by ID
     */
    async getApplication(applicationId) {
        try {
            if (this.applicationCache.has(applicationId)) {
                return this.applicationCache.get(applicationId);
            }
            
            const data = await this.readFile(this.applicationsFile);
            const application = data.applications[applicationId];
            
            if (application) {
                this.applicationCache.set(applicationId, application);
            }
            
            return application || null;
        } catch (error) {
            console.error('[FORM_STORAGE] Error getting application:', error);
            throw error;
        }
    }

    /**
     * Get applications by guild and optional filters
     */
    async getApplicationsByGuild(guildId, filters = {}) {
        try {
            const data = await this.readFile(this.applicationsFile);
            const applications = Object.values(data.applications)
                .filter(app => app.guildId === guildId);
            
            // Apply filters
            let filtered = applications;
            
            if (filters.status) {
                filtered = filtered.filter(app => app.status === filters.status);
            }
            
            if (filters.configId) {
                filtered = filtered.filter(app => app.configId === filters.configId);
            }
            
            if (filters.userId) {
                filtered = filtered.filter(app => app.userId === filters.userId);
            }
            
            if (filters.dateFrom) {
                filtered = filtered.filter(app => app.submittedAt >= filters.dateFrom);
            }
            
            if (filters.dateTo) {
                filtered = filtered.filter(app => app.submittedAt <= filters.dateTo);
            }
            
            return filtered.sort((a, b) => b.submittedAt - a.submittedAt);
        } catch (error) {
            console.error('[FORM_STORAGE] Error getting applications by guild:', error);
            throw error;
        }
    }

    /**
     * Update application status and details
     */
    async updateApplication(applicationId, updates) {
        try {
            const data = await this.readFile(this.applicationsFile);

            if (data.applications[applicationId]) {
                data.applications[applicationId] = {
                    ...data.applications[applicationId],
                    ...updates,
                    updatedAt: Date.now()
                };

                await this.writeFile(this.applicationsFile, data);
                this.applicationCache.set(applicationId, data.applications[applicationId]);

                console.log(`[FORM_STORAGE] Updated application ${applicationId}`);
                return data.applications[applicationId];
            }

            return null;
        } catch (error) {
            console.error('[FORM_STORAGE] Error updating application:', error);
            throw error;
        }
    }

    // ==================== VERIFICATION DATA METHODS ====================

    /**
     * Save verification data for a guild
     */
    async saveVerificationData(guildId, channelId, data) {
        try {
            const fileData = await this.readFile(this.verificationDataFile);

            if (!fileData.guilds[guildId]) {
                fileData.guilds[guildId] = {};
            }

            if (!fileData.guilds[guildId][channelId]) {
                fileData.guilds[guildId][channelId] = [];
            }

            // Add timestamp to each data entry
            const timestampedData = data.map(entry => ({
                ...entry,
                addedAt: entry.addedAt || Date.now(),
                id: entry.id || uuidv4()
            }));

            fileData.guilds[guildId][channelId] = timestampedData;

            await this.writeFile(this.verificationDataFile, fileData);
            this.verificationCache.set(`${guildId}_${channelId}`, timestampedData);

            console.log(`[FORM_STORAGE] Saved verification data for guild ${guildId}, channel ${channelId}`);
            return timestampedData;
        } catch (error) {
            console.error('[FORM_STORAGE] Error saving verification data:', error);
            throw error;
        }
    }

    /**
     * Get verification data for a guild and channel
     */
    async getVerificationData(guildId, channelId) {
        try {
            const cacheKey = `${guildId}_${channelId}`;
            if (this.verificationCache.has(cacheKey)) {
                return this.verificationCache.get(cacheKey);
            }

            const data = await this.readFile(this.verificationDataFile);
            const verificationData = data.guilds[guildId]?.[channelId] || [];

            this.verificationCache.set(cacheKey, verificationData);
            return verificationData;
        } catch (error) {
            console.error('[FORM_STORAGE] Error getting verification data:', error);
            throw error;
        }
    }

    /**
     * Add single verification entry
     */
    async addVerificationEntry(guildId, channelId, entry) {
        try {
            const existingData = await this.getVerificationData(guildId, channelId);
            const newEntry = {
                ...entry,
                addedAt: Date.now(),
                id: uuidv4()
            };

            existingData.push(newEntry);
            await this.saveVerificationData(guildId, channelId, existingData);

            return newEntry;
        } catch (error) {
            console.error('[FORM_STORAGE] Error adding verification entry:', error);
            throw error;
        }
    }

    // ==================== AUDIT LOG METHODS ====================

    /**
     * Add audit log entry
     */
    async addAuditLog(entry) {
        try {
            const data = await this.readFile(this.auditLogFile);

            const logEntry = {
                id: uuidv4(),
                timestamp: Date.now(),
                ...entry
            };

            data.logs.push(logEntry);

            // Keep only last 10000 entries to prevent file from growing too large
            if (data.logs.length > 10000) {
                data.logs = data.logs.slice(-10000);
            }

            await this.writeFile(this.auditLogFile, data);

            console.log(`[FORM_STORAGE] Added audit log entry: ${logEntry.action}`);
            return logEntry;
        } catch (error) {
            console.error('[FORM_STORAGE] Error adding audit log:', error);
            throw error;
        }
    }

    /**
     * Get audit logs with filters
     */
    async getAuditLogs(filters = {}) {
        try {
            const data = await this.readFile(this.auditLogFile);
            let logs = data.logs;

            // Apply filters
            if (filters.guildId) {
                logs = logs.filter(log => log.guildId === filters.guildId);
            }

            if (filters.userId) {
                logs = logs.filter(log => log.userId === filters.userId);
            }

            if (filters.action) {
                logs = logs.filter(log => log.action === filters.action);
            }

            if (filters.dateFrom) {
                logs = logs.filter(log => log.timestamp >= filters.dateFrom);
            }

            if (filters.dateTo) {
                logs = logs.filter(log => log.timestamp <= filters.dateTo);
            }

            if (filters.limit) {
                logs = logs.slice(-filters.limit);
            }

            return logs.sort((a, b) => b.timestamp - a.timestamp);
        } catch (error) {
            console.error('[FORM_STORAGE] Error getting audit logs:', error);
            throw error;
        }
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Clear cache for a specific guild
     */
    clearGuildCache(guildId) {
        for (const [key] of this.configCache) {
            if (key.startsWith(`${guildId}_`)) {
                this.configCache.delete(key);
            }
        }

        for (const [key] of this.verificationCache) {
            if (key.startsWith(`${guildId}_`)) {
                this.verificationCache.delete(key);
            }
        }

        console.log(`[FORM_STORAGE] Cleared cache for guild ${guildId}`);
    }

    /**
     * Get storage statistics
     */
    async getStorageStats() {
        try {
            const [configs, applications, verificationData, auditLogs] = await Promise.all([
                this.readFile(this.configFile),
                this.readFile(this.applicationsFile),
                this.readFile(this.verificationDataFile),
                this.readFile(this.auditLogFile)
            ]);

            return {
                guilds: Object.keys(configs.guilds).length,
                formConfigs: Object.values(configs.guilds).reduce((total, guild) => total + Object.keys(guild).length, 0),
                applications: Object.keys(applications.applications).length,
                verificationChannels: Object.values(verificationData.guilds).reduce((total, guild) => total + Object.keys(guild).length, 0),
                auditLogEntries: auditLogs.logs.length,
                cacheSize: {
                    configs: this.configCache.size,
                    applications: this.applicationCache.size,
                    verification: this.verificationCache.size
                }
            };
        } catch (error) {
            console.error('[FORM_STORAGE] Error getting storage stats:', error);
            throw error;
        }
    }
}

// Create singleton instance
const formApplicationStorage = new FormApplicationStorage();

module.exports = formApplicationStorage;
