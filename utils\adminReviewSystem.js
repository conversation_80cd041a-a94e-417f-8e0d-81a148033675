/**
 * Admin Review System
 * Comprehensive logging and review interfaces for administrators
 */

const {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder,
    MessageFlags
} = require('discord.js');

const formApplicationStorage = require('./formApplicationStorage');
const roleAssignmentEngine = require('./roleAssignmentEngine');
const { APPLICATION_STATUS } = require('./formConfigModels');

class AdminReviewSystem {
    constructor() {
        this.reviewSessions = new Map();
        this.setupCleanupInterval();
    }

    /**
     * Setup cleanup interval for review sessions
     */
    setupCleanupInterval() {
        setInterval(() => {
            const now = Date.now();
            const maxAge = 60 * 60 * 1000; // 1 hour
            
            for (const [sessionId, session] of this.reviewSessions.entries()) {
                if (now - session.createdAt > maxAge) {
                    this.reviewSessions.delete(sessionId);
                    console.log(`[ADMIN_REVIEW] Cleaned up expired session: ${sessionId}`);
                }
            }
        }, 10 * 60 * 1000); // Check every 10 minutes
    }

    /**
     * Create admin dashboard for application review
     */
    async createAdminDashboard(interaction, filters = {}) {
        try {
            const guildId = interaction.guild.id;
            const applications = await formApplicationStorage.getApplicationsByGuild(guildId, filters);
            
            const sessionId = `${interaction.user.id}_review_${Date.now()}`;
            const session = {
                sessionId,
                userId: interaction.user.id,
                guildId,
                filters,
                currentPage: 0,
                applicationsPerPage: 5,
                createdAt: Date.now()
            };
            
            this.reviewSessions.set(sessionId, session);
            
            return await this.renderDashboard(interaction, session, applications);
            
        } catch (error) {
            console.error('[ADMIN_REVIEW] Error creating admin dashboard:', error);
            throw error;
        }
    }

    /**
     * Render the admin dashboard
     */
    async renderDashboard(interaction, session, applications) {
        const startIndex = session.currentPage * session.applicationsPerPage;
        const endIndex = startIndex + session.applicationsPerPage;
        const pageApplications = applications.slice(startIndex, endIndex);
        
        const embed = new EmbedBuilder()
            .setTitle('🛡️ Admin Review Dashboard')
            .setDescription(
                'Review and manage form applications.\n\n' +
                `**Total Applications:** ${applications.length}\n` +
                `**Page:** ${session.currentPage + 1}/${Math.ceil(applications.length / session.applicationsPerPage) || 1}\n` +
                `**Showing:** ${startIndex + 1}-${Math.min(endIndex, applications.length)} of ${applications.length}`
            )
            .setColor(0x3498DB)
            .setFooter({ text: 'Admin Review System' })
            .setTimestamp();

        // Add application summaries
        if (pageApplications.length > 0) {
            pageApplications.forEach((app, index) => {
                const globalIndex = startIndex + index + 1;
                const statusEmoji = this.getStatusEmoji(app.status);
                const timeAgo = this.getTimeAgo(app.submittedAt);
                
                embed.addFields({
                    name: `${globalIndex}. ${statusEmoji} Application ${app.id.substring(0, 8)}`,
                    value: `**User:** <@${app.userId}> (${app.username})\n` +
                           `**Status:** ${this.getStatusDisplay(app.status)}\n` +
                           `**Submitted:** ${timeAgo}\n` +
                           `**Verification:** ${app.verificationPassed ? '✅ Passed' : '❌ Failed'} (${app.verificationScore?.toFixed(1) || 0}%)\n` +
                           `**Roles:** ${app.assignedRoles?.length || 0} assigned`,
                    inline: false
                });
            });
        } else {
            embed.addFields({
                name: 'No Applications',
                value: 'No applications found matching the current filters.',
                inline: false
            });
        }

        const components = this.createDashboardComponents(session, applications, pageApplications);
        
        if (interaction.replied || interaction.deferred) {
            await interaction.followUp({ embeds: [embed], components, flags: MessageFlags.Ephemeral });
        } else {
            await interaction.reply({ embeds: [embed], components, flags: MessageFlags.Ephemeral });
        }
        
        return session.sessionId;
    }

    /**
     * Create dashboard components
     */
    createDashboardComponents(session, allApplications, pageApplications) {
        const components = [];
        
        // Application selection dropdown
        if (pageApplications.length > 0) {
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`admin_select_app_${session.sessionId}`)
                .setPlaceholder('Select an application to review...')
                .addOptions(
                    pageApplications.map((app, index) => {
                        const globalIndex = session.currentPage * session.applicationsPerPage + index + 1;
                        return new StringSelectMenuOptionBuilder()
                            .setLabel(`${globalIndex}. ${app.id.substring(0, 8)} - ${app.username}`)
                            .setValue(app.id)
                            .setDescription(`${this.getStatusDisplay(app.status)} • ${this.getTimeAgo(app.submittedAt)}`)
                            .setEmoji(this.getStatusEmoji(app.status));
                    })
                );
            
            components.push(new ActionRowBuilder().addComponents(selectMenu));
        }
        
        // Navigation and action buttons
        const navRow = new ActionRowBuilder();
        
        // Previous page button
        if (session.currentPage > 0) {
            navRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`admin_prev_page_${session.sessionId}`)
                    .setLabel('Previous')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );
        }
        
        // Next page button
        const totalPages = Math.ceil(allApplications.length / session.applicationsPerPage);
        if (session.currentPage < totalPages - 1) {
            navRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`admin_next_page_${session.sessionId}`)
                    .setLabel('Next')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('➡️')
            );
        }
        
        // Filter button
        navRow.addComponents(
            new ButtonBuilder()
                .setCustomId(`admin_filter_${session.sessionId}`)
                .setLabel('Filter')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('🔍')
        );
        
        // Refresh button
        navRow.addComponents(
            new ButtonBuilder()
                .setCustomId(`admin_refresh_${session.sessionId}`)
                .setLabel('Refresh')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('🔄')
        );
        
        if (navRow.components.length > 0) {
            components.push(navRow);
        }
        
        // Bulk actions row
        const bulkRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`admin_bulk_approve_${session.sessionId}`)
                    .setLabel('Bulk Approve')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅')
                    .setDisabled(pageApplications.length === 0),
                new ButtonBuilder()
                    .setCustomId(`admin_bulk_reject_${session.sessionId}`)
                    .setLabel('Bulk Reject')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
                    .setDisabled(pageApplications.length === 0),
                new ButtonBuilder()
                    .setCustomId(`admin_export_${session.sessionId}`)
                    .setLabel('Export Data')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📤')
                    .setDisabled(allApplications.length === 0)
            );
        
        components.push(bulkRow);
        
        return components;
    }

    /**
     * Show detailed application review
     */
    async showApplicationReview(interaction, applicationId, sessionId) {
        try {
            const application = await formApplicationStorage.getApplication(applicationId);
            if (!application) {
                return await interaction.reply({
                    content: '❌ Application not found.',
                    flags: MessageFlags.Ephemeral
                });
            }

            // Get form configuration
            const formConfig = await formApplicationStorage.getFormConfig(application.guildId, application.configId);
            if (!formConfig) {
                return await interaction.reply({
                    content: '❌ Form configuration not found.',
                    flags: MessageFlags.Ephemeral
                });
            }

            const embed = new EmbedBuilder()
                .setTitle(`📋 Application Review - ${application.id.substring(0, 8)}`)
                .setDescription(`**Form:** ${formConfig.name}\n**Applicant:** <@${application.userId}> (${application.username})`)
                .setColor(this.getStatusColor(application.status))
                .addFields(
                    {
                        name: '📊 Status',
                        value: this.getStatusDisplay(application.status),
                        inline: true
                    },
                    {
                        name: '📅 Submitted',
                        value: `<t:${Math.floor(application.submittedAt / 1000)}:R>`,
                        inline: true
                    },
                    {
                        name: '🔍 Verification',
                        value: `${application.verificationPassed ? '✅ Passed' : '❌ Failed'} (${application.verificationScore?.toFixed(1) || 0}%)`,
                        inline: true
                    }
                );

            // Add verification details if available
            if (application.verificationResults && Object.keys(application.verificationResults).length > 0) {
                const verificationDetails = Object.entries(application.verificationResults)
                    .map(([questionId, result]) => {
                        const question = formConfig.questions.find(q => q.id === questionId);
                        return `• **${question?.label || 'Unknown'}:** ${result.passed ? '✅' : '❌'} (${result.score}%)`;
                    })
                    .join('\n');
                
                embed.addFields({
                    name: '🔍 Verification Details',
                    value: verificationDetails,
                    inline: false
                });
            }

            // Add assigned roles
            if (application.assignedRoles && application.assignedRoles.length > 0) {
                const roleDetails = application.assignedRoles
                    .map(role => `• **${role.roleName}** (${role.ruleName}) - ${role.score}%`)
                    .join('\n');
                
                embed.addFields({
                    name: '🏷️ Assigned Roles',
                    value: roleDetails,
                    inline: false
                });
            }

            // Add answers (limited to prevent embed size issues)
            const questions = formConfig.questions.sort((a, b) => a.order - b.order).slice(0, 8);
            questions.forEach(question => {
                const answer = application.answers[question.id] || 'No answer';
                embed.addFields({
                    name: `❓ ${question.label}`,
                    value: answer.length > 200 ? answer.substring(0, 200) + '...' : answer,
                    inline: false
                });
            });

            if (formConfig.questions.length > 8) {
                embed.addFields({
                    name: '...',
                    value: `And ${formConfig.questions.length - 8} more questions`,
                    inline: false
                });
            }

            // Add processing notes if available
            if (application.processingNotes) {
                embed.addFields({
                    name: '📝 Processing Notes',
                    value: application.processingNotes,
                    inline: false
                });
            }

            embed.setFooter({ text: `Application ID: ${application.id}` })
                .setTimestamp();

            const components = this.createApplicationReviewComponents(application, sessionId);

            await interaction.update({ embeds: [embed], components });

        } catch (error) {
            console.error('[ADMIN_REVIEW] Error showing application review:', error);
            await interaction.reply({
                content: `❌ Error loading application: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    }

    /**
     * Create application review components
     */
    createApplicationReviewComponents(application, sessionId) {
        const components = [];
        
        // Action buttons based on current status
        const actionRow = new ActionRowBuilder();
        
        if (application.status === APPLICATION_STATUS.PENDING || application.status === APPLICATION_STATUS.UNDER_REVIEW) {
            actionRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`admin_approve_${sessionId}_${application.id}`)
                    .setLabel('Approve')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅'),
                new ButtonBuilder()
                    .setCustomId(`admin_reject_${sessionId}_${application.id}`)
                    .setLabel('Reject')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );
        }
        
        if (application.status === APPLICATION_STATUS.APPROVED) {
            actionRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`admin_revoke_${sessionId}_${application.id}`)
                    .setLabel('Revoke Approval')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🔄')
            );
        }
        
        if (application.status === APPLICATION_STATUS.REJECTED) {
            actionRow.addComponents(
                new ButtonBuilder()
                    .setCustomId(`admin_reopen_${sessionId}_${application.id}`)
                    .setLabel('Reopen')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔄')
            );
        }
        
        // Always available actions
        actionRow.addComponents(
            new ButtonBuilder()
                .setCustomId(`admin_add_note_${sessionId}_${application.id}`)
                .setLabel('Add Note')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('📝')
        );
        
        if (actionRow.components.length > 0) {
            components.push(actionRow);
        }
        
        // Navigation row
        const navRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`admin_back_dashboard_${sessionId}`)
                    .setLabel('Back to Dashboard')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('⬅️'),
                new ButtonBuilder()
                    .setCustomId(`admin_view_full_${sessionId}_${application.id}`)
                    .setLabel('View Full Details')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📄'),
                new ButtonBuilder()
                    .setCustomId(`admin_contact_user_${sessionId}_${application.id}`)
                    .setLabel('Contact User')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('💬')
            );
        
        components.push(navRow);
        
        return components;
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Get status emoji
     */
    getStatusEmoji(status) {
        const emojis = {
            [APPLICATION_STATUS.PENDING]: '⏳',
            [APPLICATION_STATUS.APPROVED]: '✅',
            [APPLICATION_STATUS.REJECTED]: '❌',
            [APPLICATION_STATUS.UNDER_REVIEW]: '👥',
            [APPLICATION_STATUS.REQUIRES_VERIFICATION]: '🔍'
        };
        return emojis[status] || '❓';
    }

    /**
     * Get status display text
     */
    getStatusDisplay(status) {
        const displays = {
            [APPLICATION_STATUS.PENDING]: 'Pending',
            [APPLICATION_STATUS.APPROVED]: 'Approved',
            [APPLICATION_STATUS.REJECTED]: 'Rejected',
            [APPLICATION_STATUS.UNDER_REVIEW]: 'Under Review',
            [APPLICATION_STATUS.REQUIRES_VERIFICATION]: 'Requires Verification'
        };
        return displays[status] || status;
    }

    /**
     * Get status color
     */
    getStatusColor(status) {
        const colors = {
            [APPLICATION_STATUS.PENDING]: 0xF39C12,
            [APPLICATION_STATUS.APPROVED]: 0x27AE60,
            [APPLICATION_STATUS.REJECTED]: 0xE74C3C,
            [APPLICATION_STATUS.UNDER_REVIEW]: 0x3498DB,
            [APPLICATION_STATUS.REQUIRES_VERIFICATION]: 0xE67E22
        };
        return colors[status] || 0x95A5A6;
    }

    /**
     * Get time ago string
     */
    getTimeAgo(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        
        if (days > 0) return `${days}d ago`;
        if (hours > 0) return `${hours}h ago`;
        if (minutes > 0) return `${minutes}m ago`;
        return 'Just now';
    }

    /**
     * Get review session
     */
    getReviewSession(sessionId) {
        return this.reviewSessions.get(sessionId);
    }

    /**
     * Update review session
     */
    updateReviewSession(sessionId, updates) {
        const session = this.reviewSessions.get(sessionId);
        if (session) {
            Object.assign(session, updates);
            this.reviewSessions.set(sessionId, session);
        }
        return session;
    }

    /**
     * Delete review session
     */
    deleteReviewSession(sessionId) {
        return this.reviewSessions.delete(sessionId);
    }
}

// Create singleton instance
const adminReviewSystem = new AdminReviewSystem();

module.exports = adminReviewSystem;
