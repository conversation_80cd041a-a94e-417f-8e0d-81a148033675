{"logs": [{"id": "7a19329c-4a4f-4087-b6d7-f16416773d25", "timestamp": 1753040368984, "action": "test_action", "guildId": "test_guild_123", "userId": "test_user_456", "details": {"test": "data"}}, {"id": "20583213-e54b-443a-881f-152f1bbc294a", "timestamp": 1753040368988, "action": "role_assignment_determined", "guildId": "test_guild_123", "userId": "test_user_456", "applicationId": "d9a3eb8e-0869-4b80-8558-01529a42357d", "details": {"matchedRules": 1, "failedRules": 0, "assignedRoles": 1, "roles": ["Test Role 1"]}}, {"id": "24eb4596-cbc3-426b-a588-bc344e2fd26e", "timestamp": 1753040368992, "action": "application_submitted", "guildId": "test_guild_123", "userId": "test_user_456", "applicationId": "6f97b943-cb99-410b-89c0-0597c12190ca", "details": {"formName": "Simple Test Form", "questionCount": 1, "verificationEnabled": false}}, {"id": "d5a16b2f-7f49-420d-a4d5-e7f1c462792a", "timestamp": 1753040368993, "action": "role_assignment_determined", "guildId": "test_guild_123", "userId": "test_user_456", "applicationId": "6f97b943-cb99-410b-89c0-0597c12190ca", "details": {"matchedRules": 0, "failedRules": 0, "assignedRoles": 0, "roles": []}}, {"id": "ec035f44-e60f-4beb-9b6b-7c2faa062706", "timestamp": 1753040629486, "action": "application_submitted", "guildId": "demo_guild_12345", "userId": "demo_user_67890", "applicationId": "5a837976-3218-4d56-8244-4ff84857f950", "details": {"formName": "Member Application", "questionCount": 4, "verificationEnabled": true}}, {"id": "fdd9179d-3e73-4cfe-9049-1a82a0607385", "timestamp": 1753040629490, "action": "role_assignment_determined", "guildId": "demo_guild_12345", "userId": "demo_user_67890", "applicationId": "5a837976-3218-4d56-8244-4ff84857f950", "details": {"matchedRules": 0, "failedRules": 1, "assignedRoles": 0, "roles": []}}, {"id": "da8c728a-10e4-4573-b16a-4e65e678e825", "timestamp": 1753040629497, "action": "application_submitted", "guildId": "demo_guild_12345", "userId": "demo_user_67890", "applicationId": "f935cfe3-127a-477e-b711-0f7dac526006", "details": {"formName": "Team Selection", "questionCount": 3, "verificationEnabled": false}}, {"id": "44fcc460-5cdd-44d2-aec8-451a97d33c83", "timestamp": 1753040629499, "action": "role_assignment_determined", "guildId": "demo_guild_12345", "userId": "demo_user_67890", "applicationId": "f935cfe3-127a-477e-b711-0f7dac526006", "details": {"matchedRules": 2, "failedRules": 2, "assignedRoles": 2, "roles": ["Team Alpha", "Experienced"]}}]}