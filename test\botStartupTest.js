/**
 * Bot Startup Test
 * Tests that the bot can start and register commands properly
 */

const fs = require('fs');
const path = require('path');

class BotStartupTest {
    constructor() {
        this.testResults = [];
    }

    /**
     * Run startup tests
     */
    async runTests() {
        console.log('🧪 Testing Bot Startup and Command Registration...\n');

        try {
            await this.testCommandLoading();
            await this.testCommandRegistration();
            await this.testInteractionHandlers();

            this.printTestResults();

        } catch (error) {
            console.error('❌ Bot startup test suite failed:', error);
        }
    }

    /**
     * Test command loading
     */
    async testCommandLoading() {
        console.log('📂 Testing Command Loading...');

        try {
            // Test that commands directory exists
            const commandsPath = path.join(__dirname, '..', 'commands');
            const commandsExist = fs.existsSync(commandsPath);
            this.assert(commandsExist, 'Commands directory should exist');

            // Test that command files exist
            const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));
            this.assert(commandFiles.length > 0, 'Should have command files');
            console.log(`  📁 Found ${commandFiles.length} command files`);

            // Test loading each command
            const commands = [];
            for (const file of commandFiles) {
                const filePath = path.join(commandsPath, file);
                const command = require(filePath);

                // Check if the command has required properties
                if ('data' in command && 'execute' in command) {
                    commands.push({
                        name: command.data.name,
                        description: command.data.description,
                        file: file
                    });
                    console.log(`  ✅ Loaded: ${command.data.name} (${file})`);
                } else {
                    console.log(`  ❌ Invalid: ${file} - missing data or execute property`);
                }
            }

            // Verify application-setup command exists
            const setupCommand = commands.find(cmd => cmd.name === 'application-setup');
            this.assert(setupCommand !== undefined, 'application-setup command should exist');
            console.log(`  🎯 Found application-setup command: ${setupCommand.description}`);

            this.addTestResult('Command Loading', true, `Successfully loaded ${commands.length} commands`);

        } catch (error) {
            this.addTestResult('Command Loading', false, error.message);
        }
    }

    /**
     * Test command registration data
     */
    async testCommandRegistration() {
        console.log('📋 Testing Command Registration Data...');

        try {
            // Load the setup command specifically
            const setupCommand = require('../commands/setup.js');
            
            // Test command data structure
            this.assert(setupCommand.data, 'Command should have data property');
            this.assert(setupCommand.data.name === 'application-setup', 'Command name should be application-setup');
            this.assert(setupCommand.data.description, 'Command should have description');
            this.assert(typeof setupCommand.execute === 'function', 'Command should have execute function');

            // Test command can be converted to JSON (required for Discord registration)
            const commandJSON = setupCommand.data.toJSON();
            this.assert(commandJSON.name === 'application-setup', 'JSON should have correct name');
            this.assert(commandJSON.description, 'JSON should have description');
            this.assert(commandJSON.default_member_permissions, 'JSON should have permissions');

            console.log(`  📝 Command JSON structure:`);
            console.log(`    Name: ${commandJSON.name}`);
            console.log(`    Description: ${commandJSON.description}`);
            console.log(`    Permissions: ${commandJSON.default_member_permissions}`);

            this.addTestResult('Command Registration', true, 'Command registration data is valid');

        } catch (error) {
            this.addTestResult('Command Registration', false, error.message);
        }
    }

    /**
     * Test interaction handlers
     */
    async testInteractionHandlers() {
        console.log('🔗 Testing Interaction Handlers...');

        try {
            // Load index.js and check for interaction handling
            const indexPath = path.join(__dirname, '..', 'index.js');
            const indexContent = fs.readFileSync(indexPath, 'utf8');

            // Check for required interaction handlers
            const requiredHandlers = [
                'interaction.isChatInputCommand()',
                'interaction.isButton()',
                'interaction.isStringSelectMenu()',
                'interaction.isModalSubmit()',
                'setup_',
                'application-setup'
            ];

            for (const handler of requiredHandlers) {
                const hasHandler = indexContent.includes(handler);
                this.assert(hasHandler, `Should have ${handler} handler`);
                console.log(`  ✅ Found: ${handler}`);
            }

            // Check for setup button handling
            const hasSetupButtons = indexContent.includes('setup_') && indexContent.includes('handleSetupButton');
            this.assert(hasSetupButtons, 'Should have setup button handling');

            // Check for setup modal handling
            const hasSetupModals = indexContent.includes('setup_modal_') && indexContent.includes('handleSetupModal');
            this.assert(hasSetupModals, 'Should have setup modal handling');

            // Check for setup form select handling
            const hasSetupSelects = indexContent.includes('setup_form_select_') && indexContent.includes('handleFormSelection');
            this.assert(hasSetupSelects, 'Should have setup form select handling');

            this.addTestResult('Interaction Handlers', true, 'All required interaction handlers found');

        } catch (error) {
            this.addTestResult('Interaction Handlers', false, error.message);
        }
    }

    /**
     * Add test result
     */
    addTestResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed,
            message
        });

        const status = passed ? '✅' : '❌';
        console.log(`  ${status} ${testName}: ${message}`);
    }

    /**
     * Assert condition
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(`Assertion failed: ${message}`);
        }
    }

    /**
     * Print test results summary
     */
    printTestResults() {
        console.log('\n📊 Bot Startup Test Results:');
        console.log('============================');

        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        const failed = total - passed;

        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${failed}`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

        if (failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(r => !r.passed)
                .forEach(r => console.log(`  • ${r.name}: ${r.message}`));
        }

        console.log('\n🎉 Bot startup test completed!');
        
        if (passed === total) {
            console.log('\n✅ SUCCESS: Bot startup system is working correctly!');
            console.log('\n🔧 Startup Features Verified:');
            console.log('  • Command file loading and validation');
            console.log('  • Command registration data structure');
            console.log('  • Interaction handler routing');
            console.log('  • Setup command integration');
            console.log('\n🚀 The bot should start and register commands successfully!');
            console.log('\n📋 Next Steps:');
            console.log('  1. Start the bot with: node index.js');
            console.log('  2. Check console for "Successfully registered application commands globally"');
            console.log('  3. Test /application-setup command in Discord');
        } else {
            console.log('\n⚠️  Some tests failed. Please review the issues above.');
        }
    }
}

// Export for use
module.exports = BotStartupTest;

// Run tests if this file is executed directly
if (require.main === module) {
    const testSuite = new BotStartupTest();
    testSuite.runTests().catch(console.error);
}
