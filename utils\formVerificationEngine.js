/**
 * Form Verification Engine
 * Handles intelligent matching of form responses against stored verification data
 */

const formApplicationStorage = require('./formApplicationStorage');
const { VERIFICATION_CRITERIA } = require('./formConfigModels');

class FormVerificationEngine {
    constructor() {
        this.matchingAlgorithms = {
            exact: this.exactMatch.bind(this),
            levenshtein: this.levenshteinMatch.bind(this),
            wordOverlap: this.wordOverlapMatch.bind(this),
            substring: this.substringMatch.bind(this),
            contains: this.containsMatch.bind(this),
            regex: this.regexMatch.bind(this),
            dateRange: this.dateRangeMatch.bind(this),
            numberRange: this.numberRangeMatch.bind(this)
        };
        
        // Algorithm weights based on user preferences
        this.algorithmWeights = {
            exact: 1.0,
            levenshtein: 0.8,
            wordOverlap: 0.7,
            substring: 0.6,
            contains: 0.5,
            regex: 0.9,
            dateRange: 1.0,
            numberRange: 1.0
        };
    }

    /**
     * Verify application answers against stored verification data
     */
    async verifyApplication(application, formConfig) {
        try {
            console.log(`[VERIFICATION] Starting verification for application ${application.id}`);
            
            const verificationResults = {};
            let totalScore = 0;
            let verificationCount = 0;
            let allPassed = true;

            // Get verification questions
            const verificationQuestions = formConfig.questions.filter(q => q.isVerificationCriteria);
            
            if (verificationQuestions.length === 0) {
                console.log('[VERIFICATION] No verification criteria configured');
                return {
                    success: true,
                    passed: true,
                    score: 100,
                    results: {},
                    message: 'No verification criteria configured'
                };
            }

            // Process each verification question
            for (const question of verificationQuestions) {
                const answer = application.answers[question.id];
                if (!answer) {
                    console.log(`[VERIFICATION] No answer provided for verification question: ${question.label}`);
                    continue;
                }

                const result = await this.verifyAnswer(
                    answer,
                    question,
                    formConfig.guildId,
                    formConfig.verificationChannels,
                    formConfig.similarityThreshold,
                    formConfig.strictMatching
                );

                verificationResults[question.id] = result;
                
                if (result.passed) {
                    totalScore += result.score;
                } else {
                    allPassed = false;
                }
                
                verificationCount++;
                
                console.log(`[VERIFICATION] Question "${question.label}": ${result.passed ? 'PASSED' : 'FAILED'} (${result.score}%)`);
            }

            const averageScore = verificationCount > 0 ? totalScore / verificationCount : 0;
            const overallPassed = allPassed && averageScore >= (formConfig.strictMatching ? 85 : formConfig.similarityThreshold);

            console.log(`[VERIFICATION] Overall result: ${overallPassed ? 'PASSED' : 'FAILED'} (${averageScore.toFixed(1)}%)`);

            return {
                success: true,
                passed: overallPassed,
                score: averageScore,
                results: verificationResults,
                message: overallPassed ? 'Verification passed' : 'Verification failed'
            };

        } catch (error) {
            console.error('[VERIFICATION] Error during verification:', error);
            return {
                success: false,
                passed: false,
                score: 0,
                results: {},
                error: error.message
            };
        }
    }

    /**
     * Verify a single answer against verification data
     */
    async verifyAnswer(answer, question, guildId, verificationChannels, threshold, strictMatching) {
        try {
            const verificationConfig = question.verificationConfig;
            if (!verificationConfig) {
                return { passed: true, score: 100, message: 'No verification config' };
            }

            // Get verification data from specified channels
            let verificationData = [];
            for (const channelId of verificationChannels) {
                const channelData = await formApplicationStorage.getVerificationData(guildId, channelId);
                verificationData = verificationData.concat(channelData);
            }

            if (verificationData.length === 0) {
                console.log(`[VERIFICATION] No verification data found for channels: ${verificationChannels.join(', ')}`);
                return { passed: false, score: 0, message: 'No verification data available' };
            }

            // Extract relevant field data
            const fieldData = verificationData
                .map(entry => entry[verificationConfig.sourceField])
                .filter(value => value !== undefined && value !== null && value !== '');

            if (fieldData.length === 0) {
                return { passed: false, score: 0, message: `No data found for field: ${verificationConfig.sourceField}` };
            }

            // Perform verification based on type
            const result = await this.performVerification(
                answer,
                fieldData,
                verificationConfig,
                threshold,
                strictMatching
            );

            return result;

        } catch (error) {
            console.error('[VERIFICATION] Error verifying answer:', error);
            return { passed: false, score: 0, error: error.message };
        }
    }

    /**
     * Perform verification using the specified method
     */
    async performVerification(answer, fieldData, verificationConfig, threshold, strictMatching) {
        const actualThreshold = strictMatching ? Math.max(85, threshold) : threshold;
        
        switch (verificationConfig.type) {
            case VERIFICATION_CRITERIA.EXACT_MATCH:
                return this.exactMatchVerification(answer, fieldData);
                
            case VERIFICATION_CRITERIA.FUZZY_MATCH:
                return this.fuzzyMatchVerification(answer, fieldData, actualThreshold);
                
            case VERIFICATION_CRITERIA.CONTAINS:
                return this.containsVerification(answer, fieldData);
                
            case VERIFICATION_CRITERIA.REGEX:
                return this.regexVerification(answer, verificationConfig.customLogic);
                
            case VERIFICATION_CRITERIA.DATE_RANGE:
                return this.dateRangeVerification(answer, verificationConfig.customLogic);
                
            case VERIFICATION_CRITERIA.NUMBER_RANGE:
                return this.numberRangeVerification(answer, verificationConfig.customLogic);
                
            default:
                throw new Error(`Unknown verification type: ${verificationConfig.type}`);
        }
    }

    /**
     * Exact match verification
     */
    exactMatchVerification(answer, fieldData) {
        const normalizedAnswer = answer.toLowerCase().trim();
        const matches = fieldData.filter(data => 
            data.toLowerCase().trim() === normalizedAnswer
        );
        
        return {
            passed: matches.length > 0,
            score: matches.length > 0 ? 100 : 0,
            matches: matches.slice(0, 3),
            message: matches.length > 0 ? 'Exact match found' : 'No exact match found'
        };
    }

    /**
     * Fuzzy match verification using multiple algorithms
     */
    fuzzyMatchVerification(answer, fieldData, threshold) {
        let bestScore = 0;
        let bestMatches = [];
        
        for (const data of fieldData) {
            const scores = {
                exact: this.exactMatch(answer, data),
                levenshtein: this.levenshteinMatch(answer, data),
                wordOverlap: this.wordOverlapMatch(answer, data),
                substring: this.substringMatch(answer, data)
            };
            
            // Calculate weighted average
            const usedWeights = Object.keys(scores).map(alg => this.algorithmWeights[alg] || 0);
            const totalWeight = usedWeights.reduce((a, b) => a + b, 0);
            const weightedScore = totalWeight > 0 ?
                Object.entries(scores).reduce((total, [algorithm, score]) => {
                    return total + (score * (this.algorithmWeights[algorithm] || 0));
                }, 0) / totalWeight : 0;
            
            if (weightedScore > bestScore) {
                bestScore = weightedScore;
                bestMatches = [{ data, score: weightedScore, breakdown: scores }];
            } else if (Math.abs(weightedScore - bestScore) < 1) {
                bestMatches.push({ data, score: weightedScore, breakdown: scores });
            }
        }
        
        // Keep only top 3 matches
        bestMatches = bestMatches.slice(0, 3);
        
        return {
            passed: bestScore >= threshold,
            score: Math.round(bestScore),
            matches: bestMatches,
            message: bestScore >= threshold ? 
                `Fuzzy match found (${Math.round(bestScore)}%)` : 
                `No sufficient match found (best: ${Math.round(bestScore)}%)`
        };
    }

    /**
     * Contains verification
     */
    containsVerification(answer, fieldData) {
        const normalizedAnswer = answer.toLowerCase().trim();
        const matches = fieldData.filter(data => 
            data.toLowerCase().includes(normalizedAnswer) || 
            normalizedAnswer.includes(data.toLowerCase())
        );
        
        return {
            passed: matches.length > 0,
            score: matches.length > 0 ? 75 : 0,
            matches: matches.slice(0, 3),
            message: matches.length > 0 ? 'Contains match found' : 'No contains match found'
        };
    }

    /**
     * Regex verification
     */
    regexVerification(answer, pattern) {
        try {
            const regex = new RegExp(pattern, 'i');
            const matches = regex.test(answer);
            
            return {
                passed: matches,
                score: matches ? 100 : 0,
                matches: matches ? [answer] : [],
                message: matches ? 'Regex pattern matched' : 'Regex pattern did not match'
            };
        } catch (error) {
            return {
                passed: false,
                score: 0,
                matches: [],
                error: `Invalid regex pattern: ${error.message}`
            };
        }
    }

    /**
     * Date range verification
     */
    dateRangeVerification(answer, range) {
        try {
            const answerDate = new Date(answer);
            const startDate = new Date(range.start);
            const endDate = new Date(range.end);
            
            const inRange = answerDate >= startDate && answerDate <= endDate;
            
            return {
                passed: inRange,
                score: inRange ? 100 : 0,
                matches: inRange ? [answer] : [],
                message: inRange ? 'Date within range' : 'Date outside allowed range'
            };
        } catch (error) {
            return {
                passed: false,
                score: 0,
                matches: [],
                error: `Invalid date format: ${error.message}`
            };
        }
    }

    /**
     * Number range verification
     */
    numberRangeVerification(answer, range) {
        try {
            const answerNumber = Number(answer);
            if (isNaN(answerNumber)) {
                throw new Error('Not a valid number');
            }
            
            const inRange = answerNumber >= range.min && answerNumber <= range.max;
            
            return {
                passed: inRange,
                score: inRange ? 100 : 0,
                matches: inRange ? [answer] : [],
                message: inRange ? 'Number within range' : 'Number outside allowed range'
            };
        } catch (error) {
            return {
                passed: false,
                score: 0,
                matches: [],
                error: `Invalid number format: ${error.message}`
            };
        }
    }

    // ==================== MATCHING ALGORITHMS ====================

    /**
     * Exact match algorithm
     */
    exactMatch(str1, str2) {
        return str1.toLowerCase().trim() === str2.toLowerCase().trim() ? 100 : 0;
    }

    /**
     * Levenshtein distance algorithm
     */
    levenshteinMatch(str1, str2) {
        const s1 = str1.toLowerCase().trim();
        const s2 = str2.toLowerCase().trim();
        
        if (s1 === s2) return 100;
        
        const matrix = Array(s2.length + 1).fill().map(() => Array(s1.length + 1).fill(0));
        
        for (let i = 0; i <= s1.length; i++) matrix[0][i] = i;
        for (let j = 0; j <= s2.length; j++) matrix[j][0] = j;
        
        for (let j = 1; j <= s2.length; j++) {
            for (let i = 1; i <= s1.length; i++) {
                const cost = s1[i - 1] === s2[j - 1] ? 0 : 1;
                matrix[j][i] = Math.min(
                    matrix[j - 1][i] + 1,
                    matrix[j][i - 1] + 1,
                    matrix[j - 1][i - 1] + cost
                );
            }
        }
        
        const maxLength = Math.max(s1.length, s2.length);
        const distance = matrix[s2.length][s1.length];
        return Math.max(0, Math.round((1 - distance / maxLength) * 100));
    }

    /**
     * Word overlap algorithm
     */
    wordOverlapMatch(str1, str2) {
        const words1 = str1.toLowerCase().trim().split(/\s+/);
        const words2 = str2.toLowerCase().trim().split(/\s+/);
        
        const intersection = words1.filter(word => words2.includes(word));
        const union = [...new Set([...words1, ...words2])];
        
        return union.length > 0 ? Math.round((intersection.length / union.length) * 100) : 0;
    }

    /**
     * Substring match algorithm
     */
    substringMatch(str1, str2) {
        const s1 = str1.toLowerCase().trim();
        const s2 = str2.toLowerCase().trim();
        
        if (s1.includes(s2) || s2.includes(s1)) {
            const shorter = s1.length < s2.length ? s1 : s2;
            const longer = s1.length >= s2.length ? s1 : s2;
            return Math.round((shorter.length / longer.length) * 100);
        }
        
        return 0;
    }

    /**
     * Contains match algorithm
     */
    containsMatch(str1, str2) {
        const s1 = str1.toLowerCase().trim();
        const s2 = str2.toLowerCase().trim();
        
        return s1.includes(s2) || s2.includes(s1) ? 75 : 0;
    }

    /**
     * Regex match algorithm
     */
    regexMatch(str, pattern) {
        try {
            const regex = new RegExp(pattern, 'i');
            return regex.test(str) ? 100 : 0;
        } catch (error) {
            return 0;
        }
    }

    /**
     * Date range match algorithm
     */
    dateRangeMatch(dateStr, range) {
        try {
            const date = new Date(dateStr);
            const startDate = new Date(range.start);
            const endDate = new Date(range.end);

            if (date >= startDate && date <= endDate) {
                return 100;
            }
            return 0;
        } catch (error) {
            return 0;
        }
    }

    /**
     * Number range match algorithm
     */
    numberRangeMatch(numberStr, range) {
        try {
            const number = Number(numberStr);
            if (isNaN(number)) return 0;

            if (number >= range.min && number <= range.max) {
                return 100;
            }
            return 0;
        } catch (error) {
            return 0;
        }
    }
}

// Create singleton instance
const formVerificationEngine = new FormVerificationEngine();

module.exports = formVerificationEngine;
