/**
 * Batch Processor
 * Handles batch operations for applications and advanced admin tools
 */

const {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder,
    MessageFlags
} = require('discord.js');

const formApplicationStorage = require('./formApplicationStorage');
const roleAssignmentEngine = require('./roleAssignmentEngine');
const { APPLICATION_STATUS } = require('./formConfigModels');

class BatchProcessor {
    constructor() {
        this.batchOperations = new Map();
        this.setupCleanupInterval();
    }

    /**
     * Setup cleanup interval for batch operations
     */
    setupCleanupInterval() {
        setInterval(() => {
            const now = Date.now();
            const maxAge = 2 * 60 * 60 * 1000; // 2 hours
            
            for (const [operationId, operation] of this.batchOperations.entries()) {
                if (now - operation.startTime > maxAge) {
                    this.batchOperations.delete(operationId);
                    console.log(`[BATCH_PROCESSOR] Cleaned up expired operation: ${operationId}`);
                }
            }
        }, 15 * 60 * 1000); // Check every 15 minutes
    }

    /**
     * Start batch approval process
     */
    async startBatchApproval(interaction, filters = {}) {
        try {
            const guildId = interaction.guild.id;
            const applications = await formApplicationStorage.getApplicationsByGuild(guildId, {
                ...filters,
                status: APPLICATION_STATUS.PENDING
            });

            if (applications.length === 0) {
                return await interaction.reply({
                    content: '❌ No pending applications found matching the criteria.',
                    flags: MessageFlags.Ephemeral
                });
            }

            const operationId = `batch_approve_${guildId}_${Date.now()}`;
            const operation = {
                id: operationId,
                type: 'batch_approve',
                guildId,
                userId: interaction.user.id,
                applications,
                startTime: Date.now(),
                status: 'pending_confirmation'
            };

            this.batchOperations.set(operationId, operation);

            const embed = new EmbedBuilder()
                .setTitle('📋 Batch Approval Confirmation')
                .setDescription(
                    `You are about to approve **${applications.length}** pending applications.\n\n` +
                    '**This will:**\n' +
                    '• Change status to "Approved"\n' +
                    '• Assign configured roles to users\n' +
                    '• Send approval notifications (if enabled)\n' +
                    '• Log all actions for audit trail\n\n' +
                    '**Applications to be approved:**'
                )
                .setColor(0xF39C12);

            // Show first 10 applications
            const previewApps = applications.slice(0, 10);
            previewApps.forEach((app, index) => {
                embed.addFields({
                    name: `${index + 1}. ${app.username}`,
                    value: `ID: ${app.id.substring(0, 8)} • Submitted: <t:${Math.floor(app.submittedAt / 1000)}:R>`,
                    inline: false
                });
            });

            if (applications.length > 10) {
                embed.addFields({
                    name: '...',
                    value: `And ${applications.length - 10} more applications`,
                    inline: false
                });
            }

            embed.setFooter({ text: `Operation ID: ${operationId}` });

            const components = [
                new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`batch_confirm_approve_${operationId}`)
                            .setLabel('Confirm Batch Approval')
                            .setStyle(ButtonStyle.Success)
                            .setEmoji('✅'),
                        new ButtonBuilder()
                            .setCustomId(`batch_cancel_${operationId}`)
                            .setLabel('Cancel')
                            .setStyle(ButtonStyle.Secondary)
                            .setEmoji('❌')
                    )
            ];

            await interaction.reply({ embeds: [embed], components, flags: MessageFlags.Ephemeral });
            return operationId;

        } catch (error) {
            console.error('[BATCH_PROCESSOR] Error starting batch approval:', error);
            throw error;
        }
    }

    /**
     * Execute batch approval
     */
    async executeBatchApproval(interaction, operationId) {
        try {
            const operation = this.batchOperations.get(operationId);
            if (!operation || operation.type !== 'batch_approve') {
                return await interaction.reply({
                    content: '❌ Batch operation not found or expired.',
                    flags: MessageFlags.Ephemeral
                });
            }

            operation.status = 'processing';
            this.batchOperations.set(operationId, operation);

            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('⏳ Processing Batch Approval...')
                        .setDescription(`Processing ${operation.applications.length} applications. This may take a few moments.`)
                        .setColor(0xF39C12)
                ],
                components: []
            });

            const results = {
                successful: [],
                failed: [],
                roleAssignments: [],
                notifications: []
            };

            // Process each application
            for (const application of operation.applications) {
                try {
                    // Update application status
                    await formApplicationStorage.updateApplication(application.id, {
                        status: APPLICATION_STATUS.APPROVED,
                        reviewedBy: operation.userId,
                        reviewNotes: 'Batch approval',
                        processedAt: Date.now()
                    });

                    // Get form configuration for role assignment
                    const formConfig = await formApplicationStorage.getFormConfig(
                        application.guildId, 
                        application.configId
                    );

                    if (formConfig && application.assignedRoles && application.assignedRoles.length > 0) {
                        try {
                            const member = await interaction.guild.members.fetch(application.userId);
                            const roleResult = await roleAssignmentEngine.applyRoleAssignments(
                                member,
                                application.assignedRoles,
                                'Batch approval'
                            );

                            if (roleResult.success) {
                                results.roleAssignments.push({
                                    userId: application.userId,
                                    roles: application.assignedRoles.map(r => r.roleName),
                                    success: true
                                });
                            } else {
                                results.roleAssignments.push({
                                    userId: application.userId,
                                    error: roleResult.error,
                                    success: false
                                });
                            }
                        } catch (memberError) {
                            console.log(`[BATCH_PROCESSOR] Could not fetch member ${application.userId}: ${memberError.message}`);
                        }
                    }

                    // Send notification if configured
                    if (formConfig && formConfig.sendDMOnApproval) {
                        try {
                            const user = await interaction.client.users.fetch(application.userId);
                            const message = formConfig.approvalMessage || 'Your application has been approved!';
                            
                            const notificationEmbed = new EmbedBuilder()
                                .setTitle(`✅ Application Approved - ${formConfig.name}`)
                                .setDescription(message)
                                .setColor(0x27AE60)
                                .addFields(
                                    {
                                        name: 'Server',
                                        value: interaction.guild.name,
                                        inline: true
                                    },
                                    {
                                        name: 'Application ID',
                                        value: application.id,
                                        inline: true
                                    }
                                )
                                .setFooter({ text: 'Form Application System' })
                                .setTimestamp();

                            await user.send({ embeds: [notificationEmbed] });
                            results.notifications.push({
                                userId: application.userId,
                                success: true
                            });
                        } catch (dmError) {
                            results.notifications.push({
                                userId: application.userId,
                                error: dmError.message,
                                success: false
                            });
                        }
                    }

                    results.successful.push(application);

                } catch (error) {
                    console.error(`[BATCH_PROCESSOR] Error processing application ${application.id}:`, error);
                    results.failed.push({
                        application,
                        error: error.message
                    });
                }
            }

            // Log the batch operation
            await formApplicationStorage.addAuditLog({
                action: 'batch_approval_completed',
                guildId: operation.guildId,
                userId: operation.userId,
                details: {
                    operationId,
                    totalApplications: operation.applications.length,
                    successful: results.successful.length,
                    failed: results.failed.length,
                    roleAssignments: results.roleAssignments.length,
                    notifications: results.notifications.filter(n => n.success).length
                }
            });

            // Create results embed
            const resultsEmbed = new EmbedBuilder()
                .setTitle('✅ Batch Approval Complete')
                .setDescription(
                    `Batch approval operation completed.\n\n` +
                    `**Results:**\n` +
                    `• **Successful:** ${results.successful.length}\n` +
                    `• **Failed:** ${results.failed.length}\n` +
                    `• **Role Assignments:** ${results.roleAssignments.filter(r => r.success).length} successful\n` +
                    `• **Notifications Sent:** ${results.notifications.filter(n => n.success).length}\n\n` +
                    (results.failed.length > 0 ? 
                        `**Failed Applications:**\n${results.failed.map(f => `• ${f.application.username}: ${f.error}`).slice(0, 5).join('\n')}${results.failed.length > 5 ? `\n• And ${results.failed.length - 5} more...` : ''}` : 
                        'All applications processed successfully!')
                )
                .setColor(results.failed.length > 0 ? 0xE67E22 : 0x27AE60)
                .setFooter({ text: `Operation ID: ${operationId}` })
                .setTimestamp();

            await interaction.followUp({ embeds: [resultsEmbed], flags: MessageFlags.Ephemeral });

            // Clean up operation
            operation.status = 'completed';
            operation.results = results;
            operation.completedAt = Date.now();
            this.batchOperations.set(operationId, operation);

            return results;

        } catch (error) {
            console.error('[BATCH_PROCESSOR] Error executing batch approval:', error);
            await interaction.followUp({
                content: `❌ Error during batch approval: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
            throw error;
        }
    }

    /**
     * Start batch rejection process
     */
    async startBatchRejection(interaction, filters = {}, reason = '') {
        try {
            const guildId = interaction.guild.id;
            const applications = await formApplicationStorage.getApplicationsByGuild(guildId, {
                ...filters,
                status: APPLICATION_STATUS.PENDING
            });

            if (applications.length === 0) {
                return await interaction.reply({
                    content: '❌ No pending applications found matching the criteria.',
                    flags: MessageFlags.Ephemeral
                });
            }

            const operationId = `batch_reject_${guildId}_${Date.now()}`;
            const operation = {
                id: operationId,
                type: 'batch_reject',
                guildId,
                userId: interaction.user.id,
                applications,
                reason,
                startTime: Date.now(),
                status: 'pending_confirmation'
            };

            this.batchOperations.set(operationId, operation);

            const embed = new EmbedBuilder()
                .setTitle('❌ Batch Rejection Confirmation')
                .setDescription(
                    `You are about to reject **${applications.length}** pending applications.\n\n` +
                    '**This will:**\n' +
                    '• Change status to "Rejected"\n' +
                    '• Send rejection notifications (if enabled)\n' +
                    '• Log all actions for audit trail\n\n' +
                    (reason ? `**Reason:** ${reason}\n\n` : '') +
                    '**Applications to be rejected:**'
                )
                .setColor(0xE74C3C);

            // Show first 10 applications
            const previewApps = applications.slice(0, 10);
            previewApps.forEach((app, index) => {
                embed.addFields({
                    name: `${index + 1}. ${app.username}`,
                    value: `ID: ${app.id.substring(0, 8)} • Submitted: <t:${Math.floor(app.submittedAt / 1000)}:R>`,
                    inline: false
                });
            });

            if (applications.length > 10) {
                embed.addFields({
                    name: '...',
                    value: `And ${applications.length - 10} more applications`,
                    inline: false
                });
            }

            embed.setFooter({ text: `Operation ID: ${operationId}` });

            const components = [
                new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(`batch_confirm_reject_${operationId}`)
                            .setLabel('Confirm Batch Rejection')
                            .setStyle(ButtonStyle.Danger)
                            .setEmoji('❌'),
                        new ButtonBuilder()
                            .setCustomId(`batch_cancel_${operationId}`)
                            .setLabel('Cancel')
                            .setStyle(ButtonStyle.Secondary)
                            .setEmoji('🔄')
                    )
            ];

            await interaction.reply({ embeds: [embed], components, flags: MessageFlags.Ephemeral });
            return operationId;

        } catch (error) {
            console.error('[BATCH_PROCESSOR] Error starting batch rejection:', error);
            throw error;
        }
    }

    /**
     * Execute batch rejection
     */
    async executeBatchRejection(interaction, operationId) {
        try {
            const operation = this.batchOperations.get(operationId);
            if (!operation || operation.type !== 'batch_reject') {
                return await interaction.reply({
                    content: '❌ Batch operation not found or expired.',
                    flags: MessageFlags.Ephemeral
                });
            }

            operation.status = 'processing';
            this.batchOperations.set(operationId, operation);

            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('⏳ Processing Batch Rejection...')
                        .setDescription(`Processing ${operation.applications.length} applications. This may take a few moments.`)
                        .setColor(0xF39C12)
                ],
                components: []
            });

            const results = {
                successful: [],
                failed: [],
                notifications: []
            };

            // Process each application
            for (const application of operation.applications) {
                try {
                    // Update application status
                    await formApplicationStorage.updateApplication(application.id, {
                        status: APPLICATION_STATUS.REJECTED,
                        reviewedBy: operation.userId,
                        reviewNotes: operation.reason || 'Batch rejection',
                        processedAt: Date.now()
                    });

                    // Send notification if configured
                    const formConfig = await formApplicationStorage.getFormConfig(
                        application.guildId, 
                        application.configId
                    );

                    if (formConfig && formConfig.sendDMOnRejection) {
                        try {
                            const user = await interaction.client.users.fetch(application.userId);
                            const message = formConfig.rejectionMessage || 'Your application has been rejected.';
                            
                            const notificationEmbed = new EmbedBuilder()
                                .setTitle(`❌ Application Rejected - ${formConfig.name}`)
                                .setDescription(message + (operation.reason ? `\n\n**Reason:** ${operation.reason}` : ''))
                                .setColor(0xE74C3C)
                                .addFields(
                                    {
                                        name: 'Server',
                                        value: interaction.guild.name,
                                        inline: true
                                    },
                                    {
                                        name: 'Application ID',
                                        value: application.id,
                                        inline: true
                                    }
                                )
                                .setFooter({ text: 'Form Application System' })
                                .setTimestamp();

                            await user.send({ embeds: [notificationEmbed] });
                            results.notifications.push({
                                userId: application.userId,
                                success: true
                            });
                        } catch (dmError) {
                            results.notifications.push({
                                userId: application.userId,
                                error: dmError.message,
                                success: false
                            });
                        }
                    }

                    results.successful.push(application);

                } catch (error) {
                    console.error(`[BATCH_PROCESSOR] Error processing application ${application.id}:`, error);
                    results.failed.push({
                        application,
                        error: error.message
                    });
                }
            }

            // Log the batch operation
            await formApplicationStorage.addAuditLog({
                action: 'batch_rejection_completed',
                guildId: operation.guildId,
                userId: operation.userId,
                details: {
                    operationId,
                    totalApplications: operation.applications.length,
                    successful: results.successful.length,
                    failed: results.failed.length,
                    reason: operation.reason,
                    notifications: results.notifications.filter(n => n.success).length
                }
            });

            // Create results embed
            const resultsEmbed = new EmbedBuilder()
                .setTitle('❌ Batch Rejection Complete')
                .setDescription(
                    `Batch rejection operation completed.\n\n` +
                    `**Results:**\n` +
                    `• **Successful:** ${results.successful.length}\n` +
                    `• **Failed:** ${results.failed.length}\n` +
                    `• **Notifications Sent:** ${results.notifications.filter(n => n.success).length}\n\n` +
                    (results.failed.length > 0 ? 
                        `**Failed Applications:**\n${results.failed.map(f => `• ${f.application.username}: ${f.error}`).slice(0, 5).join('\n')}${results.failed.length > 5 ? `\n• And ${results.failed.length - 5} more...` : ''}` : 
                        'All applications processed successfully!')
                )
                .setColor(results.failed.length > 0 ? 0xE67E22 : 0xE74C3C)
                .setFooter({ text: `Operation ID: ${operationId}` })
                .setTimestamp();

            await interaction.followUp({ embeds: [resultsEmbed], flags: MessageFlags.Ephemeral });

            // Clean up operation
            operation.status = 'completed';
            operation.results = results;
            operation.completedAt = Date.now();
            this.batchOperations.set(operationId, operation);

            return results;

        } catch (error) {
            console.error('[BATCH_PROCESSOR] Error executing batch rejection:', error);
            await interaction.followUp({
                content: `❌ Error during batch rejection: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
            throw error;
        }
    }

    /**
     * Cancel batch operation
     */
    async cancelBatchOperation(interaction, operationId) {
        try {
            const operation = this.batchOperations.get(operationId);
            if (!operation) {
                return await interaction.reply({
                    content: '❌ Batch operation not found or already completed.',
                    flags: MessageFlags.Ephemeral
                });
            }

            this.batchOperations.delete(operationId);

            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('🔄 Batch Operation Cancelled')
                        .setDescription('The batch operation has been cancelled. No applications were processed.')
                        .setColor(0x95A5A6)
                ],
                components: []
            });

        } catch (error) {
            console.error('[BATCH_PROCESSOR] Error cancelling batch operation:', error);
            throw error;
        }
    }

    /**
     * Get batch operation status
     */
    getBatchOperation(operationId) {
        return this.batchOperations.get(operationId);
    }

    /**
     * Get all active batch operations for a guild
     */
    getActiveBatchOperations(guildId) {
        return Array.from(this.batchOperations.values())
            .filter(op => op.guildId === guildId && op.status !== 'completed');
    }
}

// Create singleton instance
const batchProcessor = new BatchProcessor();

module.exports = batchProcessor;
