/**
 * Application Processor
 * Handles the complete application processing pipeline
 */

const { Embed<PERSON><PERSON><PERSON>, MessageFlags } = require('discord.js');
const formApplicationStorage = require('./formApplicationStorage');
const formVerificationEngine = require('./formVerificationEngine');
const roleAssignmentEngine = require('./roleAssignmentEngine');
const { FormConfigModels, APPLICATION_STATUS } = require('./formConfigModels');

class ApplicationProcessor {
    constructor() {
        this.processingQueue = new Map();
        this.setupProcessingInterval();
    }

    /**
     * Setup processing interval for queued applications
     */
    setupProcessingInterval() {
        setInterval(async () => {
            await this.processQueuedApplications();
        }, 30 * 1000); // Process every 30 seconds
    }

    /**
     * Submit and process a new application
     */
    async submitApplication(answers, formConfig, user, guild) {
        try {
            console.log(`[APP_PROCESSOR] Processing application submission from ${user.tag}`);

            // Create application object
            const application = FormConfigModels.createApplication({
                configId: formConfig.id,
                guildId: guild.id,
                userId: user.id,
                username: user.tag,
                answers
            });

            // Validate application
            FormConfigModels.validateApplication(application, formConfig);

            // Save application to storage
            const applicationId = await formApplicationStorage.saveApplication(application);
            application.id = applicationId;

            // Log submission
            await formApplicationStorage.addAuditLog({
                action: 'application_submitted',
                guildId: guild.id,
                userId: user.id,
                applicationId,
                details: {
                    formName: formConfig.name,
                    questionCount: Object.keys(answers).length,
                    verificationEnabled: formConfig.verificationEnabled
                }
            });

            // Process the application
            const processingResult = await this.processApplication(application, formConfig, guild);

            return {
                success: true,
                applicationId,
                application,
                processingResult
            };

        } catch (error) {
            console.error('[APP_PROCESSOR] Error submitting application:', error);
            
            // Log error
            await formApplicationStorage.addAuditLog({
                action: 'application_submission_failed',
                guildId: guild.id,
                userId: user.id,
                details: {
                    error: error.message,
                    formName: formConfig.name
                }
            });

            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Process a single application through the complete pipeline
     */
    async processApplication(application, formConfig, guild) {
        try {
            console.log(`[APP_PROCESSOR] Processing application ${application.id}`);

            let status = APPLICATION_STATUS.PENDING;
            let verificationResult = null;
            let roleAssignments = [];
            let processingNotes = [];

            // Step 1: Verification (if enabled)
            if (formConfig.verificationEnabled) {
                console.log(`[APP_PROCESSOR] Running verification for application ${application.id}`);
                verificationResult = await formVerificationEngine.verifyApplication(application, formConfig);
                
                if (verificationResult.success) {
                    application.verificationResults = verificationResult.results;
                    application.verificationScore = verificationResult.score;
                    application.verificationPassed = verificationResult.passed;
                    
                    if (verificationResult.passed) {
                        processingNotes.push(`Verification passed (${verificationResult.score.toFixed(1)}%)`);
                    } else {
                        processingNotes.push(`Verification failed (${verificationResult.score.toFixed(1)}%)`);
                        status = APPLICATION_STATUS.REQUIRES_VERIFICATION;
                    }
                } else {
                    processingNotes.push(`Verification error: ${verificationResult.error}`);
                    status = APPLICATION_STATUS.UNDER_REVIEW;
                }
            } else {
                application.verificationPassed = true; // No verification required
                processingNotes.push('Verification disabled');
            }

            // Step 2: Role Assignment Determination
            if (application.verificationPassed || !formConfig.verificationEnabled) {
                console.log(`[APP_PROCESSOR] Determining role assignments for application ${application.id}`);
                const roleResult = await roleAssignmentEngine.determineRoleAssignments(application, formConfig, guild);
                
                if (roleResult.success) {
                    roleAssignments = roleResult.assignments;
                    application.assignedRoles = roleAssignments.map(a => ({
                        roleId: a.roleId,
                        roleName: a.roleName,
                        ruleName: a.ruleName,
                        score: a.score
                    }));
                    
                    processingNotes.push(`${roleAssignments.length} role(s) assigned`);
                } else {
                    processingNotes.push(`Role assignment error: ${roleResult.error}`);
                }
            }

            // Step 3: Determine final status
            if (status === APPLICATION_STATUS.PENDING) {
                if (formConfig.autoApprove && application.verificationPassed) {
                    status = APPLICATION_STATUS.APPROVED;
                    processingNotes.push('Auto-approved');
                } else if (formConfig.requireAdminReview) {
                    status = APPLICATION_STATUS.UNDER_REVIEW;
                    processingNotes.push('Requires admin review');
                } else {
                    status = APPLICATION_STATUS.APPROVED;
                    processingNotes.push('Approved');
                }
            }

            // Step 4: Apply roles if approved
            if (status === APPLICATION_STATUS.APPROVED && roleAssignments.length > 0) {
                try {
                    const member = await guild.members.fetch(application.userId);
                    const roleResult = await roleAssignmentEngine.applyRoleAssignments(
                        member, 
                        roleAssignments, 
                        `Form application approved: ${formConfig.name}`
                    );
                    
                    if (roleResult.success) {
                        processingNotes.push(`Applied ${roleResult.summary.successful} role(s)`);
                    } else {
                        processingNotes.push(`Role application failed: ${roleResult.error}`);
                    }
                } catch (error) {
                    console.error(`[APP_PROCESSOR] Error applying roles for application ${application.id}:`, error);
                    processingNotes.push(`Role application error: ${error.message}`);
                }
            }

            // Step 5: Update application status
            await formApplicationStorage.updateApplication(application.id, {
                status,
                verificationResults: application.verificationResults,
                verificationScore: application.verificationScore,
                verificationPassed: application.verificationPassed,
                assignedRoles: application.assignedRoles,
                processedAt: Date.now(),
                processingNotes: processingNotes.join('; ')
            });

            // Step 6: Send notifications
            await this.sendNotifications(application, formConfig, guild, status, processingNotes);

            // Step 7: Send admin log
            await this.sendAdminLog(application, formConfig, guild, status, roleAssignments, processingNotes);

            console.log(`[APP_PROCESSOR] Completed processing application ${application.id} with status: ${status}`);

            return {
                success: true,
                status,
                verificationResult,
                roleAssignments,
                processingNotes
            };

        } catch (error) {
            console.error(`[APP_PROCESSOR] Error processing application ${application.id}:`, error);
            
            // Update application with error status
            await formApplicationStorage.updateApplication(application.id, {
                status: APPLICATION_STATUS.UNDER_REVIEW,
                processingNotes: `Processing error: ${error.message}`,
                processedAt: Date.now()
            });

            return {
                success: false,
                error: error.message,
                status: APPLICATION_STATUS.UNDER_REVIEW
            };
        }
    }

    /**
     * Send notifications to the applicant
     */
    async sendNotifications(application, formConfig, guild, status, processingNotes) {
        try {
            const user = await guild.client.users.fetch(application.userId);
            if (!user) return;

            let shouldSendDM = false;
            let message = '';

            switch (status) {
                case APPLICATION_STATUS.APPROVED:
                    if (formConfig.sendDMOnApproval) {
                        shouldSendDM = true;
                        message = formConfig.approvalMessage || 'Your application has been approved!';
                    }
                    break;

                case APPLICATION_STATUS.REJECTED:
                    if (formConfig.sendDMOnRejection) {
                        shouldSendDM = true;
                        message = formConfig.rejectionMessage || 'Your application has been rejected.';
                    }
                    break;

                case APPLICATION_STATUS.UNDER_REVIEW:
                    shouldSendDM = true;
                    message = 'Your application is under review. You will be notified when a decision is made.';
                    break;

                case APPLICATION_STATUS.REQUIRES_VERIFICATION:
                    shouldSendDM = true;
                    message = 'Your application requires additional verification. Please contact an administrator.';
                    break;
            }

            if (shouldSendDM) {
                const embed = new EmbedBuilder()
                    .setTitle(`📋 Application Update - ${formConfig.name}`)
                    .setDescription(message)
                    .setColor(this.getStatusColor(status))
                    .addFields(
                        {
                            name: 'Status',
                            value: this.getStatusDisplay(status),
                            inline: true
                        },
                        {
                            name: 'Server',
                            value: guild.name,
                            inline: true
                        },
                        {
                            name: 'Application ID',
                            value: application.id,
                            inline: true
                        }
                    )
                    .setFooter({ text: 'Form Application System' })
                    .setTimestamp();

                await user.send({ embeds: [embed] }).catch(error => {
                    console.log(`[APP_PROCESSOR] Could not send DM to ${user.tag}: ${error.message}`);
                });
            }

        } catch (error) {
            console.error('[APP_PROCESSOR] Error sending notifications:', error);
        }
    }

    /**
     * Send admin log to the configured log channel
     */
    async sendAdminLog(application, formConfig, guild, status, roleAssignments, processingNotes) {
        try {
            if (!formConfig.logChannel) return;

            const logChannel = guild.channels.cache.get(formConfig.logChannel);
            if (!logChannel) return;

            const user = await guild.client.users.fetch(application.userId);
            
            const embed = new EmbedBuilder()
                .setTitle('📋 New Application Processed')
                .setColor(this.getStatusColor(status))
                .addFields(
                    {
                        name: '👤 Applicant',
                        value: `${user?.tag || 'Unknown'} (${application.userId})`,
                        inline: true
                    },
                    {
                        name: '📝 Form',
                        value: formConfig.name,
                        inline: true
                    },
                    {
                        name: '📊 Status',
                        value: this.getStatusDisplay(status),
                        inline: true
                    },
                    {
                        name: '🔍 Verification',
                        value: application.verificationPassed ? 
                            `✅ Passed (${application.verificationScore?.toFixed(1) || 0}%)` : 
                            `❌ Failed (${application.verificationScore?.toFixed(1) || 0}%)`,
                        inline: true
                    },
                    {
                        name: '🏷️ Assigned Roles',
                        value: roleAssignments.length > 0 ? 
                            roleAssignments.map(r => `• ${r.roleName}`).join('\n') : 
                            'None',
                        inline: true
                    },
                    {
                        name: '📝 Processing Notes',
                        value: processingNotes.join('\n• ') || 'None',
                        inline: false
                    }
                )
                .setFooter({ text: `Application ID: ${application.id}` })
                .setTimestamp();

            // Add answers as fields (limited to prevent embed size issues)
            const questions = formConfig.questions.sort((a, b) => a.order - b.order).slice(0, 5);
            questions.forEach(question => {
                const answer = application.answers[question.id] || 'No answer';
                embed.addFields({
                    name: `❓ ${question.label}`,
                    value: answer.length > 100 ? answer.substring(0, 100) + '...' : answer,
                    inline: false
                });
            });

            if (formConfig.questions.length > 5) {
                embed.addFields({
                    name: '...',
                    value: `And ${formConfig.questions.length - 5} more questions`,
                    inline: false
                });
            }

            await logChannel.send({ embeds: [embed] });

        } catch (error) {
            console.error('[APP_PROCESSOR] Error sending admin log:', error);
        }
    }

    /**
     * Process queued applications
     */
    async processQueuedApplications() {
        // This would handle any applications that need reprocessing
        // For now, it's a placeholder for future batch processing features
    }

    /**
     * Get status color for embeds
     */
    getStatusColor(status) {
        const colors = {
            [APPLICATION_STATUS.PENDING]: 0xF39C12,
            [APPLICATION_STATUS.APPROVED]: 0x27AE60,
            [APPLICATION_STATUS.REJECTED]: 0xE74C3C,
            [APPLICATION_STATUS.UNDER_REVIEW]: 0x3498DB,
            [APPLICATION_STATUS.REQUIRES_VERIFICATION]: 0xE67E22
        };
        return colors[status] || 0x95A5A6;
    }

    /**
     * Get status display text
     */
    getStatusDisplay(status) {
        const displays = {
            [APPLICATION_STATUS.PENDING]: '⏳ Pending',
            [APPLICATION_STATUS.APPROVED]: '✅ Approved',
            [APPLICATION_STATUS.REJECTED]: '❌ Rejected',
            [APPLICATION_STATUS.UNDER_REVIEW]: '👥 Under Review',
            [APPLICATION_STATUS.REQUIRES_VERIFICATION]: '🔍 Requires Verification'
        };
        return displays[status] || status;
    }
}

// Create singleton instance
const applicationProcessor = new ApplicationProcessor();

module.exports = applicationProcessor;
