// Import required Discord.js classes
const {
  Client,
  GatewayIntentBits,
  Partials,
  Collection,
  REST,
  Routes,
  Events,
  PermissionsBitField,
  MessageFlags,
  ButtonBuilder,
  ButtonStyle,
  ActionRowBuilder,
  UserSelectMenuBuilder
} = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('./config.js');

// Import handlers
const reactionRoleHandler = require('./utils/reactionRoleHandler.js');

// Initialize client with ALL required intents
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.DirectMessages
  ],
  partials: [Partials.Channel, Partials.Message]
});

// Create a collection for commands
client.commands = new Collection();

// Add debugging for errors
process.on('unhandledRejection', error => {
  console.error('Unhandled promise rejection:', error);
});

// Load command files
const commandsPath = path.join(__dirname, 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

// Command registration array for REST API
const commands = [];

// Load each command into the client.commands collection
for (const file of commandFiles) {
  const filePath = path.join(commandsPath, file);
  const command = require(filePath);

  // Check if the command has the required properties
  if ('data' in command && 'execute' in command) {
    client.commands.set(command.data.name, command);
    commands.push(command.data.toJSON());
    console.log(`Loaded command: ${command.data.name}`);
  } else {
    console.warn(`Warning: The command at ${filePath} is missing required "data" or "execute" property.`);
  }
}

// Bot ready event
client.once(Events.ClientReady, async readyClient => {
  console.log(`Logged in as ${readyClient.user.tag}`);
  console.log('Bot is ready!');

  // Register slash commands globally when the bot starts
  try {
    console.log('Started refreshing application (/) commands.');

    const rest = new REST({ version: '10' }).setToken(config.token);

    // Register commands globally
    await rest.put(
      Routes.applicationCommands(readyClient.user.id),
      { body: commands }
    );

    console.log('Successfully registered application commands globally.');
  } catch (error) {
    console.error('Error registering commands:', error);
  }

  // List the servers the bot is in
  console.log(`Bot is in ${client.guilds.cache.size} servers`);
  client.guilds.cache.forEach(guild => {
    console.log(`- ${guild.name} (${guild.id})`);
  });
});

// Message handling
client.on(Events.MessageCreate, async message => {
  console.log(`[MESSAGE] ${message.author.tag}: ${message.content}`);

  // Ignore messages from bots
  if (message.author.bot) return;

  // Simple command to test if the bot is responsive
  if (message.content === '!ping') {
    console.log('Ping command received!');
    try {
      await message.reply('Pong!');
      console.log('Replied to ping');
    } catch (error) {
      console.error('Error replying to ping:', error);
    }
  }

  // Channel creation command (legacy text command)
  if (message.content.startsWith('!create-channel')) {
    console.log('Create channel command received!');
    const channelName = message.content.split(' ')[1];

    if (!channelName) {
      console.log('No channel name provided');
      return message.reply('Please provide a channel name!');
    }

    console.log(`Attempting to create channel: ${channelName}`);
    try {
      const newChannel = await message.guild.channels.create({
        name: channelName,
        type: 0 // Text channel
      });

      console.log(`Channel created: ${newChannel.name}`);
      await message.reply(`Successfully created channel: ${newChannel.name}`);
    } catch (error) {
      console.error(`Error creating channel:`, error);
      await message.reply(`Failed to create channel: ${error.message}`);
    }
  }
});

// Interaction handling for slash commands
client.on(Events.InteractionCreate, async interaction => {
  // Handling slash commands
  if (interaction.isChatInputCommand()) {
    console.log(`[INTERACTION] Command used: ${interaction.commandName}`);

    // Get the command from the collection
    const command = client.commands.get(interaction.commandName);

    // If command doesn't exist
    if (!command) {
      console.error(`No command matching ${interaction.commandName} was found.`);
      return interaction.reply({
        content: 'This command is not properly implemented.',
        flags: MessageFlags.Ephemeral
      });
    }

    // Execute the command
    try {
      await command.execute(interaction);
      console.log(`Executed command: ${interaction.commandName}`);
    } catch (error) {
      console.error(`Error executing ${interaction.commandName}:`);
      console.error(error);

      try {
        // If the interaction has already been replied to or deferred
        if (interaction.replied || interaction.deferred) {
          await interaction.followUp({
            content: `There was an error executing this command: ${error.message}`,
            flags: MessageFlags.Ephemeral
          }).catch(followUpError => {
            console.error('Error sending followUp after command error:', followUpError);
          });
        } else {
          await interaction.reply({
            content: `There was an error executing this command: ${error.message}`,
            flags: MessageFlags.Ephemeral
          }).catch(replyError => {
            console.error('Error sending reply after command error:', replyError);
          });
        }
      } catch (responseError) {
        console.error('Error responding to interaction after command error:', responseError);
      }
    }
  }

  // Handling button interactions
  else if (interaction.isButton()) {
    console.log(`[BUTTON] Received button click: ${interaction.customId}`);

    try {
      // Handle application setup button interactions
      if (interaction.customId.startsWith('setup_')) {
        const setupCommand = client.commands.get('application-setup');
        if (setupCommand && setupCommand.handleSetupButton) {
          await setupCommand.handleSetupButton(interaction);
          return;
        }
      }

      // Handle reaction role button interactions
      if (interaction.customId.startsWith('reaction_role_')) {
        const reactionRoleHandler = require('./utils/reactionRoleHandler');
        await reactionRoleHandler.handleReactionRoleButton(interaction);
        return;
      }

      // Handle style button interactions (for role addition workflow)
      if (interaction.customId.startsWith('style_')) {
        const setupCommand = client.commands.get('setup-reaction-roles');
        if (setupCommand && setupCommand.handleButtonStyleSelection) {
          await setupCommand.handleButtonStyleSelection(interaction);
          return;
        }
      }

      // Enhanced role matching interactions are no longer needed (fully automatic)
      if (interaction.customId.startsWith('enhanced_')) {
        await interaction.reply({
          content: '🤖 **Automatic Mode Enabled**\n\nRole matching is now fully automatic! No user interaction is required.\n\nSimply use `/bulkmanager` → "Create Private Channels" and the system will automatically:\n• Find the best matching existing roles\n• Create new roles when no good matches exist\n• Handle everything seamlessly without prompts',
          flags: MessageFlags.Ephemeral
        });
        return;
      }

      // Handle legacy role matching button interactions (for backward compatibility)
      if (interaction.customId.startsWith('role_match_')) {
        const roleMatchingHandler = require('./utils/roleMatchingHandler.old');
        await handleRoleMatchingDecision(interaction, roleMatchingHandler);
        return;
      }

      // Handle dashboard button interactions
      if (interaction.customId.startsWith('dashboard_') ||
          interaction.customId.startsWith('role_') ||
          interaction.customId.startsWith('select_role_') ||
          interaction.customId.startsWith('role_selection_')) {
        const setupCommand = client.commands.get('setup-reaction-roles');
        if (setupCommand && setupCommand.handleDashboardButton) {
          await setupCommand.handleDashboardButton(interaction);
          return;
        }
      }

      const bulkManagerCommand = client.commands.get('bulkmanager');

      if (interaction.customId === 'refreshBulkManagerMenu' || interaction.customId === 'backToBulkManager') {
        // Show the main menu again
        await bulkManagerCommand.showMainMenu(interaction, true).catch(error => {
          console.error('Error showing main menu:', error);

          // Try to respond with a simple message if the menu fails
          if (!interaction.replied && !interaction.deferred) {
            interaction.reply({
              content: 'There was an error refreshing the menu. Please try the command again.',
              flags: MessageFlags.Ephemeral
            }).catch(replyError => {
              console.error('Error sending reply after menu error:', replyError);
            });
          } else {
            interaction.followUp({
              content: 'There was an error refreshing the menu. Please try the command again.',
              flags: MessageFlags.Ephemeral
            }).catch(followUpError => {
              console.error('Error sending followUp after menu error:', followUpError);
            });
          }
        });
      }
    } catch (error) {
      console.error('Error handling button interaction:', error);

      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: `An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        } else {
          await interaction.followUp({
            content: `An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (responseError) {
        console.error('Error responding to button interaction after error:', responseError);
      }
    }
  }

  // Handling select menu interactions
  else if (interaction.isStringSelectMenu()) {
    console.log(`[SELECT MENU] Received selection: ${interaction.customId}`);

    // Handle setup form selection menu
    if (interaction.customId.startsWith('setup_form_select_')) {
      const sessionId = interaction.customId.split('_')[3];
      const setupCommand = client.commands.get('application-setup');
      if (setupCommand && setupCommand.handleFormSelection) {
        await setupCommand.handleFormSelection(interaction, sessionId);
        return;
      }
    }

    // Enhanced role matching select menus are no longer needed (fully automatic)
    if (interaction.customId.startsWith('enhanced_')) {
      await interaction.reply({
        content: '🤖 **Automatic Mode Enabled**\n\nRole matching is now fully automatic! No dropdown selections are needed.\n\nThe system automatically selects the best matching roles based on similarity scores.',
        flags: MessageFlags.Ephemeral
      });
      return;
    }

    // Handle dashboard select menu interactions
    if (interaction.customId.startsWith('select_')) {
      const setupCommand = client.commands.get('setup-reaction-roles');
      if (setupCommand && setupCommand.handleDashboardSelect) {
        await setupCommand.handleDashboardSelect(interaction);
        return;
      }
    }

    const bulkManagerCommand = client.commands.get('bulkmanager');

    // Handle bulkManagerSelect menu (string select)
    if (interaction.customId === 'bulkManagerSelect') {
      const selectedValue = interaction.values[0];

      try {
        switch (selectedValue) {
          case 'bulkRemoveRoles':
            // Show modal for removing roles from all members
            const bulkRemoveRolesModal = bulkManagerCommand.createBulkRemoveRolesModal();
            await interaction.showModal(bulkRemoveRolesModal);
            break;

          case 'bulkUserRoleManagement':
            // Show modal for bulk user role management
            const bulkUserRoleManagementModal = bulkManagerCommand.createBulkUserRoleManagementModal();
            await interaction.showModal(bulkUserRoleManagementModal);
            break;

          // Removed individual user role management cases in favor of the bulk option

          case 'bulkDeleteRoles':
            // Show modal for deleting roles
            const bulkDeleteRolesModal = bulkManagerCommand.createBulkDeleteRolesModal();
            await interaction.showModal(bulkDeleteRolesModal);
            break;

          case 'bulkDeleteRolesByPattern':
            // Show modal for deleting roles by pattern
            const bulkDeleteRolesByPatternModal = bulkManagerCommand.createBulkDeleteRolesByPatternModal();
            await interaction.showModal(bulkDeleteRolesByPatternModal);
            break;

          case 'bulkDeleteChannels':
            // Show modal for deleting channels
            const bulkDeleteChannelsModal = bulkManagerCommand.createBulkDeleteChannelsModal();
            await interaction.showModal(bulkDeleteChannelsModal);
            break;

          case 'bulkDeleteChannelsByCategory':
            // Show modal for deleting channels by category
            const bulkDeleteChannelsByCategoryModal = bulkManagerCommand.createBulkDeleteChannelsByCategoryModal();
            await interaction.showModal(bulkDeleteChannelsByCategoryModal);
            break;

          case 'bulkDeleteChannelsByPattern':
            // Show modal for deleting channels by pattern
            const bulkDeleteChannelsByPatternModal = bulkManagerCommand.createBulkDeleteChannelsByPatternModal();
            await interaction.showModal(bulkDeleteChannelsByPatternModal);
            break;

          case 'createChannels':
            // Show modal for creating channels
            const createChannelsModal = bulkManagerCommand.createChannelsModal();
            await interaction.showModal(createChannelsModal);
            break;

          case 'createPrivateChannels':
            // Show modal for creating private channels
            const createPrivateChannelsModal = bulkManagerCommand.createPrivateChannelsModal();
            await interaction.showModal(createPrivateChannelsModal);
            break;

          case 'createRoles':
            // Show modal for creating roles
            const createRolesModal = bulkManagerCommand.createRolesModal();
            await interaction.showModal(createRolesModal);
            break;

          default:
            await interaction.reply({
              content: 'Unknown operation selected.',
              flags: MessageFlags.Ephemeral
            });
        }
      } catch (error) {
        console.error('Error handling select menu interaction:', error);
        if (!interaction.replied) {
          await interaction.reply({
            content: `An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        } else {
          await interaction.followUp({
            content: `An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        }
      }
    }
  }

  // Handling modal submissions for our commands
  else if (interaction.isModalSubmit()) {
    console.log(`[MODAL] Received modal submission: ${interaction.customId}`);

    try {
      // Handle setup modal interactions
      if (interaction.customId.startsWith('setup_modal_')) {
        const setupCommand = client.commands.get('application-setup');
        if (setupCommand && setupCommand.handleSetupModal) {
          await setupCommand.handleSetupModal(interaction);
          return;
        }
      }

      // Handle dashboard modal interactions
      if (interaction.customId.startsWith('modal_')) {
        const setupCommand = client.commands.get('setup-reaction-roles');
        if (setupCommand && setupCommand.handleDashboardModal) {
          await setupCommand.handleDashboardModal(interaction);
          return;
        }
      }



      // Handle createChannelsModal submission
      if (interaction.customId === 'createChannelsModal') {
      const channelNames = interaction.fields.getTextInputValue('channelNamesInput').split('\n');
      const channelType = interaction.fields.getTextInputValue('channelTypeInput').toLowerCase();
      const categoryName = interaction.fields.getTextInputValue('categoryInput');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // Find or create category if specified
        let categoryChannel = null;
        if (categoryName) {
          categoryChannel = interaction.guild.channels.cache.find(
            channel => channel.type === 4 && channel.name.toLowerCase() === categoryName.toLowerCase()
          );

          if (!categoryChannel) {
            categoryChannel = await interaction.guild.channels.create({
              name: categoryName,
              type: 4, // Category type
            });
          }
        }

        // Create each channel
        let successCount = 0;
        const failedChannels = [];

        for (const name of channelNames) {
          if (name.trim()) {
            try {
              const channelOptions = {
                name: name.trim(),
                type: channelType === 'voice' ? 2 : 0, // 0 for text, 2 for voice
                parent: categoryChannel ? categoryChannel.id : null
              };

              const newChannel = await interaction.guild.channels.create(channelOptions);
              successCount++;
              console.log(`Created channel: ${newChannel.name}`);
            } catch (error) {
              console.error(`Failed to create channel ${name}:`, error);
              failedChannels.push(`${name} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Created ${successCount} channels successfully.`;
        if (failedChannels.length > 0) {
          response += `\nFailed to create ${failedChannels.length} channels: ${failedChannels.join(', ')}`;
        }

        await interaction.followUp({
          content: response,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing createChannelsModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkUserRoleManagementModal submission
    else if (interaction.customId === 'bulkUserRoleManagementModal') {
      const userNames = interaction.fields.getTextInputValue('userNamesInput').split('\n');
      const roleNames = interaction.fields.getTextInputValue('roleNamesInput').split('\n');
      const actionType = interaction.fields.getTextInputValue('actionTypeInput').toLowerCase();

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk user role ${actionType} request. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        // Get the bot's highest role position
        const botMember = await interaction.guild.members.fetch(interaction.client.user.id);
        const botHighestRole = botMember.roles.highest;

        console.log(`Bot's highest role: ${botHighestRole.name} (Position: ${botHighestRole.position})`);

        // Initialize counters and arrays for tracking
        let userSuccessCount = 0;
        let roleSuccessCount = 0;
        let totalOperations = 0;
        const notFoundUsers = [];
        const notFoundRoles = [];
        const failedOperations = [];
        const processedUsers = [];
        const processedRoles = [];

        // First, resolve all role names/IDs to actual roles
        const resolvedRoles = [];
        for (const roleName of roleNames) {
          if (roleName.trim()) {
            // Try to find by ID first
            let role;
            if (/^\d+$/.test(roleName.trim())) {
              // It's a numeric ID
              role = interaction.guild.roles.cache.get(roleName.trim());
            }

            // If not found by ID, try by name
            if (!role) {
              role = interaction.guild.roles.cache.find(r => r.name.toLowerCase() === roleName.trim().toLowerCase());
            }

            if (role) {
              // Check if the bot can manage this role
              if (role.position >= botHighestRole.position) {
                console.log(`Role is higher than bot's highest role and cannot be managed: ${role.name}`);
                failedOperations.push(`Role "${role.name}" is higher in hierarchy than bot's roles`);
              } else {
                resolvedRoles.push(role);
                processedRoles.push(role.name);
              }
            } else {
              console.log(`Role not found: ${roleName.trim()}`);
              notFoundRoles.push(roleName.trim());
            }
          }
        }

        // If no valid roles were found, exit early
        if (resolvedRoles.length === 0) {
          return await interaction.followUp({
            content: `No valid roles were found to ${actionType}. Please check the role names/IDs and try again.`,
            flags: MessageFlags.Ephemeral
          });
        }

        // Update the user
        await interaction.followUp({
          content: `Found ${resolvedRoles.length} valid roles: ${processedRoles.join(', ')}`,
          flags: MessageFlags.Ephemeral
        });

        // Now, resolve all user names/IDs to actual members
        for (const userName of userNames) {
          if (userName.trim()) {
            try {
              // Try to find by ID first
              let member;
              if (/^\d+$/.test(userName.trim())) {
                // It's a numeric ID
                member = await interaction.guild.members.fetch(userName.trim()).catch(() => null);
              }

              // If not found by ID, try by username
              if (!member) {
                const members = await interaction.guild.members.fetch();
                member = members.find(m =>
                  m.user.username.toLowerCase() === userName.trim().toLowerCase() ||
                  m.displayName.toLowerCase() === userName.trim().toLowerCase() ||
                  (m.nickname && m.nickname.toLowerCase() === userName.trim().toLowerCase())
                );
              }

              if (member) {
                processedUsers.push(member.user.tag);

                // Process each role for this user
                let userRoleSuccessCount = 0;

                for (const role of resolvedRoles) {
                  totalOperations++;

                  try {
                    if (actionType === 'add') {
                      // Check if user already has the role
                      if (member.roles.cache.has(role.id)) {
                        console.log(`User ${member.user.tag} already has role ${role.name}`);
                        continue;
                      }

                      // Add the role
                      await member.roles.add(role, `Bulk role addition requested by ${interaction.user.tag}`);
                      console.log(`Added role ${role.name} to ${member.user.tag}`);
                      userRoleSuccessCount++;
                      roleSuccessCount++;
                    } else {
                      // Check if user has the role
                      if (!member.roles.cache.has(role.id)) {
                        console.log(`User ${member.user.tag} doesn't have role ${role.name}`);
                        continue;
                      }

                      // Remove the role
                      await member.roles.remove(role, `Bulk role removal requested by ${interaction.user.tag}`);
                      console.log(`Removed role ${role.name} from ${member.user.tag}`);
                      userRoleSuccessCount++;
                      roleSuccessCount++;
                    }

                    // Add a small delay to avoid rate limiting
                    await new Promise(resolve => setTimeout(resolve, 300));
                  } catch (error) {
                    console.error(`Failed to ${actionType} role ${role.name} ${actionType === 'add' ? 'to' : 'from'} ${member.user.tag}:`, error);
                    failedOperations.push(`${role.name} ${actionType === 'add' ? 'to' : 'from'} ${member.user.tag} (${error.message})`);
                  }
                }

                if (userRoleSuccessCount > 0) {
                  userSuccessCount++;
                }

                // Notify about progress
                await interaction.followUp({
                  content: `Processed user ${member.user.tag}: ${userRoleSuccessCount} roles ${actionType === 'add' ? 'added' : 'removed'} successfully`,
                  flags: MessageFlags.Ephemeral
                });
              } else {
                console.log(`User not found: ${userName.trim()}`);
                notFoundUsers.push(userName.trim());
              }
            } catch (error) {
              console.error(`Error processing user ${userName.trim()}:`, error);
              failedOperations.push(`User ${userName.trim()} (${error.message})`);
            }
          }
        }

        // Create a back button
        const backButton = new ButtonBuilder()
          .setCustomId('backToBulkManager')
          .setLabel('Back to Menu')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️');

        const backButtonRow = new ActionRowBuilder().addComponents(backButton);

        // Respond with results
        let response = `Bulk user role ${actionType} operation completed.\n`;
        response += `• Processed ${processedUsers.length} users: ${processedUsers.join(', ')}\n`;
        response += `• Processed ${processedRoles.length} roles: ${processedRoles.join(', ')}\n`;
        response += `• Successfully ${actionType === 'add' ? 'added' : 'removed'} ${roleSuccessCount} roles out of ${totalOperations} operations\n`;

        if (notFoundUsers.length > 0) {
          response += `• Couldn't find ${notFoundUsers.length} users: ${notFoundUsers.join(', ')}\n`;
        }

        if (notFoundRoles.length > 0) {
          response += `• Couldn't find ${notFoundRoles.length} roles: ${notFoundRoles.join(', ')}\n`;
        }

        if (failedOperations.length > 0) {
          response += `• Failed operations: ${failedOperations.join(', ')}`;
        }

        await interaction.followUp({
          content: response,
          components: [backButtonRow],
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkUserRoleManagementModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle createRolesModal submission
    else if (interaction.customId === 'createRolesModal') {
      const roleNames = interaction.fields.getTextInputValue('roleNamesInput').split('\n');
      const roleColor = interaction.fields.getTextInputValue('roleColorInput');
      const roleVisibility = interaction.fields.getTextInputValue('roleVisibilityInput').toLowerCase();

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        console.log(`[BULK_ROLE_CREATION] Creating ${roleNames.length} roles directly`);

        // Set role options
        const roleOptions = {};
        if (roleColor && roleColor.match(/^#[0-9A-F]{6}$/i)) {
          roleOptions.color = roleColor;
        }
        roleOptions.hoist = roleVisibility === 'yes';

        // Filter and clean role names
        const cleanRoleNames = roleNames
          .map(name => name.trim())
          .filter(name => name.length > 0);

        if (cleanRoleNames.length === 0) {
          await interaction.followUp({
            content: '❌ No valid role names provided.',
            flags: MessageFlags.Ephemeral
          });
          return;
        }

        // Simple, direct role creation (no fuzzy matching, no admin confirmation)
        let successCount = 0;
        const failedRoles = [];
        const createdRoles = [];

        for (const name of cleanRoleNames) {
          try {
            // Check if role already exists
            const existingRole = interaction.guild.roles.cache.find(role =>
              role.name.toLowerCase() === name.toLowerCase()
            );

            if (existingRole) {
              failedRoles.push(`${name} (Role already exists)`);
              console.log(`[BULK_ROLE_CREATION] Skipped existing role: ${name}`);
              continue;
            }

            // Create the role
            const newRole = await interaction.guild.roles.create({
              name: name,
              ...roleOptions
            });

            successCount++;
            createdRoles.push(newRole.name);
            console.log(`[BULK_ROLE_CREATION] Created role: ${newRole.name}`);
          } catch (error) {
            console.error(`[BULK_ROLE_CREATION] Failed to create role ${name}:`, error);
            failedRoles.push(`${name} (${error.message})`);
          }
        }

        // Create comprehensive response
        let responseText = `🎉 **Bulk Role Creation Complete**\n\n`;
        responseText += `📊 **Summary:**\n`;
        responseText += `• Total roles requested: ${cleanRoleNames.length}\n`;
        responseText += `• ✅ Successfully created: ${successCount}\n`;
        responseText += `• ❌ Failed/Skipped: ${failedRoles.length}\n\n`;

        if (createdRoles.length > 0) {
          responseText += `✅ **Successfully Created:**\n`;
          createdRoles.forEach(roleName => {
            responseText += `• **${roleName}**\n`;
          });
        }

        if (failedRoles.length > 0) {
          responseText += `\n❌ **Failed/Skipped:**\n`;
          failedRoles.forEach(roleInfo => {
            responseText += `• ${roleInfo}\n`;
          });
        }

        await interaction.followUp({
          content: responseText,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing createRolesModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle createPrivateChannelsModal submission
    else if (interaction.customId === 'createPrivateChannelsModal') {
      const channelNames = interaction.fields.getTextInputValue('channelNamesInput').split('\n');
      const channelType = interaction.fields.getTextInputValue('channelTypeInput').toLowerCase();
      const categoryName = interaction.fields.getTextInputValue('categoryInput');
      const roleColor = interaction.fields.getTextInputValue('roleColorInput');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // Import automatic role matching handler
        const enhancedRoleMatchingHandler = require('./utils/enhancedRoleMatchingHandler');

        console.log(`[AUTOMATIC_PRIVATE_CHANNELS] Processing ${channelNames.length} channels automatically`);

        // Find or create category if specified
        let categoryChannel = null;
        if (categoryName) {
          categoryChannel = interaction.guild.channels.cache.find(
            channel => channel.type === 4 && channel.name.toLowerCase() === categoryName.toLowerCase()
          );

          if (!categoryChannel) {
            categoryChannel = await interaction.guild.channels.create({
              name: categoryName,
              type: 4, // Category type
            });
          }
        }

        // Set role options
        const roleOptions = {};
        if (roleColor && roleColor.match(/^#[0-9A-F]{6}$/i)) {
          roleOptions.color = roleColor;
        }

        // Create context for automatic role matching
        const context = {
            guild: interaction.guild,
            guildId: interaction.guild.id,
            userId: interaction.user.id,
            channelType: channelType === 'voice' ? 'voice' : 'text',
            categoryChannel: categoryChannel,
            roleOptions: roleOptions,
            operationType: 'private_channel_creation'
        };

        // Filter and clean channel names
        const cleanChannelNames = channelNames
            .map(name => name.trim())
            .filter(name => name.length > 0);

        if (cleanChannelNames.length === 0) {
            await interaction.followUp({
                content: '❌ No valid channel names provided.',
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Process all channels automatically (no user interaction required)
        const batchResult = await enhancedRoleMatchingHandler.processAutomaticBatchRoleMatching(
            cleanChannelNames,
            interaction.guild.roles.cache,
            context
        );

        if (!batchResult.success) {
            await interaction.followUp({
                content: `❌ Failed to process channels: ${batchResult.summary?.errors?.join(', ') || 'Unknown error'}`,
                flags: MessageFlags.Ephemeral
            });
            return;
        }

        // Create comprehensive success response
        let responseText = `🎉 **Automatic Channel Creation Complete**\n\n`;
        responseText += `📊 **Summary:**\n`;
        responseText += `• Total channels: ${batchResult.summary.total}\n`;
        responseText += `• ✅ Successful: ${batchResult.summary.successful}\n`;
        responseText += `• ❌ Failed: ${batchResult.summary.failed}\n`;
        responseText += `• 🔄 Used existing roles: ${batchResult.summary.usedExisting}\n`;
        responseText += `• ➕ Created new roles: ${batchResult.summary.createdNew}\n\n`;

        if (batchResult.summary.successful > 0) {
            responseText += `✅ **Successfully Created:**\n`;
            batchResult.results.filter(r => r.success).forEach(result => {
                const actionText = result.action === 'used_existing_role' ?
                    `Used existing role "${result.role.name}"` :
                    `Created new role "${result.role.name}"`;
                responseText += `• **${result.name}** - ${actionText}\n`;
                if (result.score) {
                    responseText += `  └ Match: ${result.score}% (${result.priority} priority)\n`;
                }
            });
        }

        if (batchResult.summary.failed > 0) {
            responseText += `\n❌ **Failed:**\n`;
            batchResult.results.filter(r => !r.success).forEach(result => {
                responseText += `• **${result.name}** - ${result.error}\n`;
            });
        }

        await interaction.followUp({
            content: responseText,
            flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing createPrivateChannelsModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkRemoveRolesModal submission
    else if (interaction.customId === 'bulkRemoveRolesModal') {
      const roleNames = interaction.fields.getTextInputValue('roleNamesInput').split('\n');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk role removal request. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        let successCount = 0;
        const failedRoles = [];
        const notFoundRoles = [];

        // Process each role
        for (const name of roleNames) {
          if (name.trim()) {
            // Find the role by name
            const role = interaction.guild.roles.cache.find(r => r.name.toLowerCase() === name.trim().toLowerCase());

            if (!role) {
              console.log(`Role not found: ${name.trim()}`);
              notFoundRoles.push(name.trim());
              continue;
            }

            try {
              // Fetch all guild members first to ensure we have the latest data
              await interaction.followUp({
                content: `Fetching members with role "${role.name}"...`,
                flags: MessageFlags.Ephemeral
              });

              // Fetch all members with this role
              const fetchedMembers = await interaction.guild.members.fetch();
              const membersWithRole = fetchedMembers.filter(member => member.roles.cache.has(role.id));

              await interaction.followUp({
                content: `Found ${membersWithRole.size} members with role "${role.name}". Removing role...`,
                flags: MessageFlags.Ephemeral
              });

              // Remove the role from each member
              let memberSuccessCount = 0;
              for (const [memberId, member] of membersWithRole) {
                try {
                  await member.roles.remove(role);
                  memberSuccessCount++;

                  // Add a small delay to avoid rate limiting
                  await new Promise(resolve => setTimeout(resolve, 100));
                } catch (memberError) {
                  console.error(`Failed to remove role ${role.name} from member ${member.user.tag}:`, memberError);
                }
              }

              console.log(`Removed role ${role.name} from ${memberSuccessCount} members`);
              successCount++;
            } catch (error) {
              console.error(`Failed to process role ${name.trim()}:`, error);
              failedRoles.push(`${name.trim()} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Removed ${successCount} roles from members successfully.`;
        if (notFoundRoles.length > 0) {
          response += `\nCouldn't find ${notFoundRoles.length} roles: ${notFoundRoles.join(', ')}`;
        }
        if (failedRoles.length > 0) {
          response += `\nFailed to process ${failedRoles.length} roles: ${failedRoles.join(', ')}`;
        }

        // Create a back button
        const backButton = new ButtonBuilder()
          .setCustomId('backToBulkManager')
          .setLabel('Back to Menu')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️');

        const backButtonRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.followUp({
          content: response,
          components: [backButtonRow],
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkRemoveRolesModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkDeleteRolesModal submission
    else if (interaction.customId === 'bulkDeleteRolesModal') {
      const roleNames = interaction.fields.getTextInputValue('roleNamesInput').split('\n');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk role deletion request. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        let successCount = 0;
        const failedRoles = [];
        const notFoundRoles = [];

        // Get the bot's highest role position
        const botMember = await interaction.guild.members.fetch(interaction.client.user.id);
        const botHighestRole = botMember.roles.highest;

        console.log(`Bot's highest role: ${botHighestRole.name} (Position: ${botHighestRole.position})`);

        // Process each role
        for (const name of roleNames) {
          if (name.trim()) {
            // Find the role by name
            const role = interaction.guild.roles.cache.find(r => r.name.toLowerCase() === name.trim().toLowerCase());

            if (!role) {
              console.log(`Role not found: ${name.trim()}`);
              notFoundRoles.push(name.trim());
              continue;
            }

            console.log(`Processing role: ${role.name} (ID: ${role.id}, Position: ${role.position}, Managed: ${role.managed})`);

            // Check if the role can be deleted
            if (role.managed) {
              console.log(`Role is managed by an integration and cannot be deleted: ${name.trim()}`);
              failedRoles.push(`${name.trim()} (Role is managed by an integration)`);
              continue;
            }

            if (role.position >= botHighestRole.position) {
              console.log(`Role is higher than bot's highest role and cannot be deleted: ${name.trim()}`);
              failedRoles.push(`${name.trim()} (Role is higher in hierarchy than bot's roles)`);
              continue;
            }

            try {
              // Notify about the role being deleted
              await interaction.followUp({
                content: `Attempting to delete role: ${role.name}...`,
                flags: MessageFlags.Ephemeral
              });

              // Delete the role
              await role.delete(`Bulk role deletion requested by ${interaction.user.tag}`);
              console.log(`Deleted role: ${name.trim()}`);
              successCount++;

              // Add a small delay to avoid rate limiting
              await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
              console.error(`Failed to delete role ${name.trim()}:`, error);
              failedRoles.push(`${name.trim()} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Deleted ${successCount} roles successfully.`;
        if (notFoundRoles.length > 0) {
          response += `\nCouldn't find ${notFoundRoles.length} roles: ${notFoundRoles.join(', ')}`;
        }
        if (failedRoles.length > 0) {
          response += `\nFailed to delete ${failedRoles.length} roles: ${failedRoles.join(', ')}`;
        }

        // Create a back button
        const backButton = new ButtonBuilder()
          .setCustomId('backToBulkManager')
          .setLabel('Back to Menu')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️');

        const backButtonRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.followUp({
          content: response,
          components: [backButtonRow],
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkDeleteRolesModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkDeleteChannelsModal submission
    else if (interaction.customId === 'bulkDeleteChannelsModal') {
      const channelNames = interaction.fields.getTextInputValue('channelNamesInput').split('\n');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk channel deletion request. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        let successCount = 0;
        const failedChannels = [];
        const notFoundChannels = [];

        // Process each channel
        for (const name of channelNames) {
          if (name.trim()) {
            // Find the channel by name (case insensitive)
            const channel = interaction.guild.channels.cache.find(
              c => c.name.toLowerCase() === name.trim().toLowerCase()
            );

            if (!channel) {
              console.log(`Channel not found: ${name.trim()}`);
              notFoundChannels.push(name.trim());
              continue;
            }

            console.log(`Processing channel: ${channel.name} (ID: ${channel.id}, Type: ${channel.type})`);

            try {
              // Notify about the channel being deleted
              await interaction.followUp({
                content: `Attempting to delete channel: ${channel.name}...`,
                flags: MessageFlags.Ephemeral
              });

              // Delete the channel
              await channel.delete(`Bulk channel deletion requested by ${interaction.user.tag}`);
              console.log(`Deleted channel: ${name.trim()}`);
              successCount++;

              // Add a small delay to avoid rate limiting
              await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
              console.error(`Failed to delete channel ${name.trim()}:`, error);
              failedChannels.push(`${name.trim()} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Deleted ${successCount} channels successfully.`;
        if (notFoundChannels.length > 0) {
          response += `\nCouldn't find ${notFoundChannels.length} channels: ${notFoundChannels.join(', ')}`;
        }
        if (failedChannels.length > 0) {
          response += `\nFailed to delete ${failedChannels.length} channels: ${failedChannels.join(', ')}`;
        }

        // Create a back button
        const backButton = new ButtonBuilder()
          .setCustomId('backToBulkManager')
          .setLabel('Back to Menu')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️');

        const backButtonRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.followUp({
          content: response,
          components: [backButtonRow],
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkDeleteChannelsModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkRemoveUserRolesModal submission
    else if (interaction.customId.startsWith('bulkRemoveUserRolesModal-')) {
      // Extract the user ID from the customId
      const userId = interaction.customId.split('-')[1];
      const roleNames = interaction.fields.getTextInputValue('roleNamesInput').split('\n');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk role removal request for user. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        // Fetch the target member
        const targetMember = await interaction.guild.members.fetch(userId);

        if (!targetMember) {
          return await interaction.followUp({
            content: `Error: Could not find the user in this server.`,
            flags: MessageFlags.Ephemeral
          });
        }

        console.log(`Processing roles for user: ${targetMember.user.tag} (ID: ${targetMember.id})`);

        // Get the bot's highest role position
        const botMember = await interaction.guild.members.fetch(interaction.client.user.id);
        const botHighestRole = botMember.roles.highest;

        console.log(`Bot's highest role: ${botHighestRole.name} (Position: ${botHighestRole.position})`);

        let successCount = 0;
        const failedRoles = [];
        const notFoundRoles = [];

        // Process each role
        for (const name of roleNames) {
          if (name.trim()) {
            // Find the role by name
            const role = interaction.guild.roles.cache.find(r => r.name.toLowerCase() === name.trim().toLowerCase());

            if (!role) {
              console.log(`Role not found: ${name.trim()}`);
              notFoundRoles.push(name.trim());
              continue;
            }

            console.log(`Processing role: ${role.name} (ID: ${role.id}, Position: ${role.position})`);

            // Check if the user has this role
            if (!targetMember.roles.cache.has(role.id)) {
              console.log(`User doesn't have role: ${name.trim()}`);
              continue;
            }

            // Check if the bot can manage this role
            if (role.position >= botHighestRole.position) {
              console.log(`Role is higher than bot's highest role and cannot be managed: ${name.trim()}`);
              failedRoles.push(`${name.trim()} (Role is higher in hierarchy than bot's roles)`);
              continue;
            }

            try {
              // Notify about the role being removed
              await interaction.followUp({
                content: `Removing role "${role.name}" from ${targetMember.user.username}...`,
                flags: MessageFlags.Ephemeral
              });

              // Remove the role from the user
              await targetMember.roles.remove(role, `Bulk role removal requested by ${interaction.user.tag}`);
              console.log(`Removed role ${role.name} from ${targetMember.user.tag}`);
              successCount++;

              // Add a small delay to avoid rate limiting
              await new Promise(resolve => setTimeout(resolve, 300));
            } catch (error) {
              console.error(`Failed to remove role ${name.trim()} from user:`, error);
              failedRoles.push(`${name.trim()} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Removed ${successCount} roles from ${targetMember.user.tag} successfully.`;
        if (notFoundRoles.length > 0) {
          response += `\nCouldn't find ${notFoundRoles.length} roles: ${notFoundRoles.join(', ')}`;
        }
        if (failedRoles.length > 0) {
          response += `\nFailed to remove ${failedRoles.length} roles: ${failedRoles.join(', ')}`;
        }

        // Create a back button
        const backButton = new ButtonBuilder()
          .setCustomId('backToBulkManager')
          .setLabel('Back to Menu')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️');

        const backButtonRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.followUp({
          content: response,
          components: [backButtonRow],
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkRemoveUserRolesModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkRemoveUserRolesByIdModal submission
    else if (interaction.customId.startsWith('bulkRemoveUserRolesByIdModal-')) {
      // Extract the user ID from the customId
      const userId = interaction.customId.split('-')[1];
      const roleIds = interaction.fields.getTextInputValue('roleIdsInput').split('\n');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // Fetch the target member
        const targetMember = await interaction.guild.members.fetch(userId);

        if (!targetMember) {
          return await interaction.followUp({
            content: `Error: Could not find the user in this server.`,
            flags: MessageFlags.Ephemeral
          });
        }

        let successCount = 0;
        const failedRoles = [];
        const notFoundRoles = [];

        // Process each role ID
        for (const id of roleIds) {
          if (id.trim()) {
            // Find the role by ID
            const role = interaction.guild.roles.cache.get(id.trim());

            if (!role) {
              console.log(`Role not found with ID: ${id.trim()}`);
              notFoundRoles.push(id.trim());
              continue;
            }

            // Check if the user has this role
            if (!targetMember.roles.cache.has(role.id)) {
              console.log(`User doesn't have role: ${role.name} (${id.trim()})`);
              continue;
            }

            try {
              // Remove the role from the user
              await targetMember.roles.remove(role, `Bulk role removal requested by ${interaction.user.tag}`);
              console.log(`Removed role ${role.name} from ${targetMember.user.tag}`);
              successCount++;
            } catch (error) {
              console.error(`Failed to remove role ${role.name} (${id.trim()}) from user:`, error);
              failedRoles.push(`${role.name} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Removed ${successCount} roles from ${targetMember.user.tag} successfully.`;
        if (notFoundRoles.length > 0) {
          response += `\nCouldn't find ${notFoundRoles.length} roles with IDs: ${notFoundRoles.join(', ')}`;
        }
        if (failedRoles.length > 0) {
          response += `\nFailed to remove ${failedRoles.length} roles: ${failedRoles.join(', ')}`;
        }

        await interaction.followUp({
          content: response,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkRemoveUserRolesByIdModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkDeleteChannelsByCategoryModal submission
    else if (interaction.customId === 'bulkDeleteChannelsByCategoryModal') {
      const categoryName = interaction.fields.getTextInputValue('categoryNameInput');
      const deleteCategoryInput = interaction.fields.getTextInputValue('deleteCategoryInput').toLowerCase();
      const deleteCategory = deleteCategoryInput === 'yes';

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // Find the category by name
        const category = interaction.guild.channels.cache.find(
          channel => channel.type === 4 && channel.name.toLowerCase() === categoryName.toLowerCase()
        );

        if (!category) {
          return await interaction.followUp({
            content: `Error: Could not find a category named "${categoryName}" in this server.`,
            flags: MessageFlags.Ephemeral
          });
        }

        // Get all channels in this category
        const channelsInCategory = interaction.guild.channels.cache.filter(
          channel => channel.parentId === category.id
        );

        if (channelsInCategory.size === 0) {
          let response = `The category "${categoryName}" doesn't contain any channels.`;

          // Delete the category if requested
          if (deleteCategory) {
            try {
              await category.delete(`Bulk category deletion requested by ${interaction.user.tag}`);
              response += `\nThe category itself has been deleted.`;
            } catch (error) {
              console.error(`Failed to delete category ${categoryName}:`, error);
              response += `\nFailed to delete the category: ${error.message}`;
            }
          }

          return await interaction.followUp({
            content: response,
            flags: MessageFlags.Ephemeral
          });
        }

        let successCount = 0;
        const failedChannels = [];

        // Delete each channel in the category
        for (const [channelId, channel] of channelsInCategory) {
          try {
            await channel.delete(`Bulk channel deletion requested by ${interaction.user.tag}`);
            console.log(`Deleted channel: ${channel.name}`);
            successCount++;
          } catch (error) {
            console.error(`Failed to delete channel ${channel.name}:`, error);
            failedChannels.push(`${channel.name} (${error.message})`);
          }
        }

        // Delete the category if requested
        let categoryDeleted = false;
        let categoryError = null;

        if (deleteCategory) {
          try {
            await category.delete(`Bulk category deletion requested by ${interaction.user.tag}`);
            categoryDeleted = true;
          } catch (error) {
            console.error(`Failed to delete category ${categoryName}:`, error);
            categoryError = error.message;
          }
        }

        // Respond with results
        let response = `Deleted ${successCount} channels from category "${categoryName}" successfully.`;
        if (failedChannels.length > 0) {
          response += `\nFailed to delete ${failedChannels.length} channels: ${failedChannels.join(', ')}`;
        }

        if (deleteCategory) {
          if (categoryDeleted) {
            response += `\nThe category itself has been deleted.`;
          } else {
            response += `\nFailed to delete the category: ${categoryError}`;
          }
        }

        await interaction.followUp({
          content: response,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkDeleteChannelsByCategoryModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkDeleteRolesByPatternModal submission
    else if (interaction.customId === 'bulkDeleteRolesByPatternModal') {
      const pattern = interaction.fields.getTextInputValue('patternInput');
      const confirmation = interaction.fields.getTextInputValue('confirmationInput');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk role deletion by pattern request. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        // Check confirmation
        if (confirmation.toLowerCase() !== 'confirm') {
          return await interaction.followUp({
            content: `Operation cancelled. You must type "confirm" to proceed with bulk role deletion.`,
            flags: MessageFlags.Ephemeral
          });
        }

        // Get the bot's highest role position
        const botMember = await interaction.guild.members.fetch(interaction.client.user.id);
        const botHighestRole = botMember.roles.highest;

        console.log(`Bot's highest role: ${botHighestRole.name} (Position: ${botHighestRole.position})`);

        // Find all roles that match the pattern
        const matchingRoles = interaction.guild.roles.cache.filter(
          role => role.name.includes(pattern) && !role.managed && role.id !== interaction.guild.id
        );

        if (matchingRoles.size === 0) {
          return await interaction.followUp({
            content: `No roles found matching the pattern "${pattern}".`,
            flags: MessageFlags.Ephemeral
          });
        }

        console.log(`Found ${matchingRoles.size} roles matching pattern "${pattern}"`);

        // Filter roles that can be deleted (not managed and lower than bot's highest role)
        const deletableRoles = matchingRoles.filter(role =>
          !role.managed && role.position < botHighestRole.position
        );

        const nonDeletableRoles = matchingRoles.filter(role =>
          role.managed || role.position >= botHighestRole.position
        );

        console.log(`${deletableRoles.size} roles can be deleted, ${nonDeletableRoles.size} roles cannot be deleted`);

        // Confirm with the user
        let confirmMessage = `Found ${matchingRoles.size} roles matching the pattern "${pattern}":\n${matchingRoles.map(r => r.name).join(', ')}`;

        if (nonDeletableRoles.size > 0) {
          confirmMessage += `\n\n⚠️ ${nonDeletableRoles.size} roles cannot be deleted due to permissions or because they are managed by integrations:\n${nonDeletableRoles.map(r => r.name).join(', ')}`;
        }

        confirmMessage += `\n\nAttempting to delete ${deletableRoles.size} roles...`;

        await interaction.followUp({
          content: confirmMessage,
          flags: MessageFlags.Ephemeral
        });

        let successCount = 0;
        const failedRoles = [];

        // Delete each matching role that can be deleted
        for (const [roleId, role] of deletableRoles) {
          try {
            // Notify about the role being deleted
            await interaction.followUp({
              content: `Attempting to delete role: ${role.name}...`,
              flags: MessageFlags.Ephemeral
            });

            await role.delete(`Bulk role deletion by pattern requested by ${interaction.user.tag}`);
            console.log(`Deleted role: ${role.name}`);
            successCount++;

            // Add a small delay to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 500));
          } catch (error) {
            console.error(`Failed to delete role ${role.name}:`, error);
            failedRoles.push(`${role.name} (${error.message})`);
          }
        }

        // Respond with results
        let response = `Deleted ${successCount} roles matching the pattern "${pattern}" successfully.`;
        if (failedRoles.length > 0) {
          response += `\nFailed to delete ${failedRoles.length} roles: ${failedRoles.join(', ')}`;
        }

        await interaction.followUp({
          content: response,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkDeleteRolesByPatternModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkDeleteChannelsByPatternModal submission
    else if (interaction.customId === 'bulkDeleteChannelsByPatternModal') {
      const pattern = interaction.fields.getTextInputValue('patternInput');
      const confirmation = interaction.fields.getTextInputValue('confirmationInput');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk channel deletion by pattern request. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        // Check confirmation
        if (confirmation.toLowerCase() !== 'confirm') {
          return await interaction.followUp({
            content: `Operation cancelled. You must type "confirm" to proceed with bulk channel deletion.`,
            flags: MessageFlags.Ephemeral
          });
        }

        // Find all channels that match the pattern (excluding categories)
        const matchingChannels = interaction.guild.channels.cache.filter(
          channel => channel.type !== 4 && channel.name.includes(pattern)
        );

        if (matchingChannels.size === 0) {
          return await interaction.followUp({
            content: `No channels found matching the pattern "${pattern}".`,
            flags: MessageFlags.Ephemeral
          });
        }

        console.log(`Found ${matchingChannels.size} channels matching pattern "${pattern}"`);

        // Confirm with the user
        await interaction.followUp({
          content: `Found ${matchingChannels.size} channels matching the pattern "${pattern}":\n${matchingChannels.map(c => c.name).join(', ')}\n\nDeleting channels...`,
          flags: MessageFlags.Ephemeral
        });

        let successCount = 0;
        const failedChannels = [];

        // Delete each matching channel
        for (const [channelId, channel] of matchingChannels) {
          try {
            // Notify about the channel being deleted
            await interaction.followUp({
              content: `Attempting to delete channel: ${channel.name}...`,
              flags: MessageFlags.Ephemeral
            });

            await channel.delete(`Bulk channel deletion by pattern requested by ${interaction.user.tag}`);
            console.log(`Deleted channel: ${channel.name}`);
            successCount++;

            // Add a small delay to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 500));
          } catch (error) {
            console.error(`Failed to delete channel ${channel.name}:`, error);
            failedChannels.push(`${channel.name} (${error.message})`);
          }
        }

        // Respond with results
        let response = `Deleted ${successCount} channels matching the pattern "${pattern}" successfully.`;
        if (failedChannels.length > 0) {
          response += `\nFailed to delete ${failedChannels.length} channels: ${failedChannels.join(', ')}`;
        }

        await interaction.followUp({
          content: response,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkDeleteChannelsByPatternModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }
    } catch (error) {
      console.error('Error handling modal interaction:', error);
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: `An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        } else {
          await interaction.followUp({
            content: `An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (responseError) {
        console.error('Error responding to modal interaction after error:', responseError);
      }
    }
  }

  // Removed individual user select menu handler in favor of the bulk user role management
});

// Cleanup old skipped roles data every 10 minutes
setInterval(() => {
  if (global.skippedRolesData) {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, data] of global.skippedRolesData.entries()) {
      // Remove data older than 10 minutes
      if (now - data.timestamp > 10 * 60 * 1000) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      global.skippedRolesData.delete(key);
      console.log(`Cleaned up expired skipped roles data: ${key}`);
    });
  }
}, 10 * 60 * 1000); // Run every 10 minutes

// Function to handle role matching decisions
async function handleRoleMatchingDecision(interaction, roleMatchingHandler) {
  const customId = interaction.customId;
  const parts = customId.split('_');
  const action = parts[2]; // use, create, replace, cancel
  const sessionId = parts.slice(3).join('_');
  
  console.log(`[ROLE_MATCHING] Handling decision: ${action} for session: ${sessionId}`);
  
  // Get session data
  const sessionData = roleMatchingHandler.getMatchingSession(sessionId);
  if (!sessionData) {
    return await interaction.reply({
      content: '❌ Session expired or not found. Please try the operation again.',
      flags: MessageFlags.Ephemeral
    });
  }
  
  // Verify user owns this session
  if (sessionData.userId !== interaction.user.id) {
    return await interaction.reply({
      content: '❌ This session belongs to another user.',
      flags: MessageFlags.Ephemeral
    });
  }
  
  try {
    await interaction.deferReply({ flags: MessageFlags.Ephemeral });
    
    switch (action) {
      case 'use':
        await handleUseExistingRole(interaction, sessionData, roleMatchingHandler, sessionId);
        break;
      case 'create':
        await handleCreateNewRole(interaction, sessionData, roleMatchingHandler, sessionId);
        break;
      case 'replace':
        await handleReplaceExistingRole(interaction, sessionData, roleMatchingHandler, sessionId);
        break;
      case 'cancel':
        await handleCancelOperation(interaction, sessionData, roleMatchingHandler, sessionId);
        break;
      default:
        await interaction.followUp({
          content: '❌ Unknown action.',
          flags: MessageFlags.Ephemeral
        });
    }
  } catch (error) {
    console.error('Error handling role matching decision:', error);
    await interaction.followUp({
      content: `❌ An error occurred: ${error.message}`,
      flags: MessageFlags.Ephemeral
    });
  }
}

// Handle using existing role
async function handleUseExistingRole(interaction, sessionData, roleMatchingHandler, sessionId) {
  const existingRole = sessionData.matches[0].role;
  
  try {
    if (sessionData.operationType === 'private_channel_creation') {
      // Create channel with existing role
      const result = await createPrivateChannelWithRole(
        interaction,
        sessionData.channelName,
        existingRole,
        sessionData.channelType,
        sessionData.categoryChannel
      );
      
      if (result.success) {
        await interaction.followUp({
          content: `✅ **Channel created successfully using existing role!**\n\n` +
                  `📍 **Channel:** ${result.channel}\n` +
                  `👥 **Role:** ${existingRole} (existing)\n` +
                  `📊 **Members with access:** ${existingRole.members.size}\n\n` +
                  `The channel is now accessible to all members who have the existing role.`,
          flags: MessageFlags.Ephemeral
        });
      } else {
        throw new Error(result.error);
      }
    } else if (sessionData.operationType === 'bulk_role_creation') {
      // For bulk role creation, just acknowledge using existing role
      await interaction.followUp({
        content: `✅ **Using existing role instead of creating new one!**\n\n` +
                `👥 **Role:** ${existingRole} (existing)\n` +
                `📊 **Current members:** ${existingRole.members.size}\n` +
                `🔄 **Action:** No new role created - existing role will be used\n\n` +
                `The existing role remains unchanged and available for use.`,
        flags: MessageFlags.Ephemeral
      });
    }
  } catch (error) {
    console.error('Error using existing role:', error);
    await interaction.followUp({
      content: `❌ Failed to use existing role: ${error.message}`,
      flags: MessageFlags.Ephemeral
    });
  }
  
  // Clean up session
  roleMatchingHandler.deleteMatchingSession(sessionId);
}

// Handle creating new role
async function handleCreateNewRole(interaction, sessionData, roleMatchingHandler, sessionId) {
  try {
    // Create new role
    const newRole = await interaction.guild.roles.create({
      name: sessionData.intendedRoleName,
      ...sessionData.roleOptions
    });
    
    if (sessionData.operationType === 'private_channel_creation') {
      // Create channel with new role
      const result = await createPrivateChannelWithRole(
        interaction,
        sessionData.channelName,
        newRole,
        sessionData.channelType,
        sessionData.categoryChannel
      );
      
      if (result.success) {
        await interaction.followUp({
          content: `✅ **New role and channel created successfully!**\n\n` +
                  `📍 **Channel:** ${result.channel}\n` +
                  `👥 **Role:** ${newRole} (new)\n` +
                  `📊 **Existing similar roles:** ${sessionData.matches.length}\n\n` +
                  `Both the new role and existing similar roles now coexist in your server.`,
          flags: MessageFlags.Ephemeral
        });
      } else {
        // If channel creation failed, clean up the role
        await newRole.delete().catch(console.error);
        throw new Error(result.error);
      }
    } else if (sessionData.operationType === 'bulk_role_creation') {
      // For bulk role creation, just confirm role creation
      await interaction.followUp({
        content: `✅ **New role created successfully!**\n\n` +
                `👥 **Role:** ${newRole} (new)\n` +
                `📊 **Existing similar roles:** ${sessionData.matches.length}\n\n` +
                `The new role has been created and coexists with similar existing roles.`,
        flags: MessageFlags.Ephemeral
      });
    }
  } catch (error) {
    console.error('Error creating new role:', error);
    const operationText = sessionData.operationType === 'private_channel_creation' ? 'role and channel' : 'role';
    await interaction.followUp({
      content: `❌ Failed to create new ${operationText}: ${error.message}`,
      flags: MessageFlags.Ephemeral
    });
  }
  
  // Clean up session
  roleMatchingHandler.deleteMatchingSession(sessionId);
}

// Handle replacing existing role
async function handleReplaceExistingRole(interaction, sessionData, roleMatchingHandler, sessionId) {
  const existingRole = sessionData.matches[0].role;
  
  try {
    // Create new role first
    const newRole = await interaction.guild.roles.create({
      name: sessionData.intendedRoleName,
      ...sessionData.roleOptions
    });
    
    if (sessionData.operationType === 'private_channel_creation') {
      // Create channel with new role
      const result = await createPrivateChannelWithRole(
        interaction,
        sessionData.channelName,
        newRole,
        sessionData.channelType,
        sessionData.categoryChannel
      );
      
      if (result.success) {
        // Delete the existing role
        const memberCount = existingRole.members.size;
        await existingRole.delete(`Replaced by new role: ${newRole.name}`);
        
        await interaction.followUp({
          content: `✅ **Role replaced and channel created successfully!**\n\n` +
                  `📍 **Channel:** ${result.channel}\n` +
                  `👥 **New Role:** ${newRole}\n` +
                  `🗑️ **Deleted Role:** \`${existingRole.name}\` (had ${memberCount} members)\n\n` +
                  `⚠️ **Note:** Members who had the old role will need to be assigned the new role to access the channel.`,
          flags: MessageFlags.Ephemeral
        });
      } else {
        // If channel creation failed, clean up the new role
        await newRole.delete().catch(console.error);
        throw new Error(result.error);
      }
    } else if (sessionData.operationType === 'bulk_role_creation') {
      // For bulk role creation, just replace the role
      const memberCount = existingRole.members.size;
      await existingRole.delete(`Replaced by new role: ${newRole.name}`);
      
      await interaction.followUp({
        content: `✅ **Role replaced successfully!**\n\n` +
                `👥 **New Role:** ${newRole}\n` +
                `🗑️ **Deleted Role:** \`${existingRole.name}\` (had ${memberCount} members)\n\n` +
                `⚠️ **Note:** Members who had the old role will need to be assigned the new role manually.`,
        flags: MessageFlags.Ephemeral
      });
    }
  } catch (error) {
    console.error('Error replacing existing role:', error);
    const operationText = sessionData.operationType === 'private_channel_creation' ? 'role, create channel' : 'role';
    await interaction.followUp({
      content: `❌ Failed to replace ${operationText}: ${error.message}`,
      flags: MessageFlags.Ephemeral
    });
  }
  
  // Clean up session
  roleMatchingHandler.deleteMatchingSession(sessionId);
}

// Handle canceling operation
async function handleCancelOperation(interaction, sessionData, roleMatchingHandler, sessionId) {
  await interaction.followUp({
    content: `❌ **Operation cancelled.**\n\nNo changes were made for channel: \`${sessionData.channelName}\``,
    flags: MessageFlags.Ephemeral
  });
  
  // Clean up session
  roleMatchingHandler.deleteMatchingSession(sessionId);
}

// Helper function to create private channel with role
async function createPrivateChannelWithRole(interaction, channelName, role, channelType, categoryChannel) {
  try {
    // Get @everyone role ID
    const everyoneRole = interaction.guild.roles.everyone;
    
    // Set permissions based on channel type
    const isVoiceChannel = channelType === 'voice';
    let allowPermissions;
    
    if (isVoiceChannel) {
      // For voice channels
      allowPermissions = [
        PermissionsBitField.Flags.ViewChannel,
        PermissionsBitField.Flags.Connect,
        PermissionsBitField.Flags.Speak
      ];
    } else {
      // For text channels
      allowPermissions = [
        PermissionsBitField.Flags.ViewChannel,
        PermissionsBitField.Flags.SendMessages,
        PermissionsBitField.Flags.ReadMessageHistory
      ];
    }
    
    // Create the channel with proper permissions
    const channelOptions = {
      name: channelName,
      type: channelType === 'voice' ? 2 : 0, // 0 for text, 2 for voice
      parent: categoryChannel ? categoryChannel.id : null,
      permissionOverwrites: [
        {
          id: everyoneRole.id,
          deny: [PermissionsBitField.Flags.ViewChannel] // Deny view access to everyone
        },
        {
          id: role.id,
          allow: allowPermissions // Allow appropriate permissions for the role
        }
      ]
    };
    
    const newChannel = await interaction.guild.channels.create(channelOptions);
    
    console.log(`Created private ${isVoiceChannel ? 'voice' : 'text'} channel: ${newChannel.name} with role: ${role.name}`);
    
    return {
      success: true,
      channel: newChannel
    };
  } catch (error) {
    console.error('Error creating private channel with role:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Login with your token
console.log('Attempting to log in...');
client.login(config.token).then(() => {
  console.log('Login successful!');
}).catch(error => {
  console.error('Login failed:', error);
});

// Export the client for potential use in other files
module.exports = { client };
