{"applications": {"48b5b114-9f8e-4bbc-b3ba-ba51a7d6b678": {"id": "48b5b114-9f8e-4bbc-b3ba-ba51a7d6b678", "configId": "758338e4-591f-4cd2-8db8-219350246564", "guildId": "test_guild_123", "userId": "test_user_456", "username": "StorageTestUser", "answers": {"test_question": "test_answer"}, "verificationResults": {}, "verificationScore": 0, "verificationPassed": false, "status": "pending", "assignedRoles": [], "reviewedBy": null, "reviewNotes": "", "submittedAt": 1753040368982, "processedAt": null, "updatedAt": 1753040368982}, "6f97b943-cb99-410b-89c0-0597c12190ca": {"id": "6f97b943-cb99-410b-89c0-0597c12190ca", "configId": "afccadc6-7309-4386-aa6f-9f58f9d2119a", "guildId": "test_guild_123", "userId": "test_user_456", "username": "TestUser#1234", "answers": {"f1cd2c41-3cea-4b70-a2cd-7e5f17347076": "Test User Name"}, "verificationResults": {}, "verificationScore": 0, "verificationPassed": true, "status": "approved", "assignedRoles": [], "reviewedBy": null, "reviewNotes": "", "submittedAt": 1753040368991, "processedAt": 1753040368994, "updatedAt": 1753040368994, "processingNotes": "Verification disabled; 0 role(s) assigned; Auto-approved"}, "5a837976-3218-4d56-8244-4ff84857f950": {"id": "5a837976-3218-4d56-8244-4ff84857f950", "configId": "fde6eacd-154d-49c7-9aa4-cf7418c01e83", "guildId": "demo_guild_12345", "userId": "demo_user_67890", "username": "DemoUser#1234", "answers": {"3f2bb862-9392-4eb8-a3cc-bbf3693699ce": "<PERSON>", "f8d7fca0-2a0b-468f-93b1-83773cbd311d": "<EMAIL>", "be16e846-fb81-4be7-832e-90aceda870a9": "JohnD#1234", "5d3a789d-cf73-4106-a3f9-193e20182aea": "I want to join because I am passionate about this community and believe I can contribute valuable insights and help others grow."}, "verificationResults": {"3f2bb862-9392-4eb8-a3cc-bbf3693699ce": {"passed": true, "score": 100, "matches": [{"data": "<PERSON>", "score": 100, "breakdown": {"exact": 100, "levenshtein": 100, "wordOverlap": 100, "substring": 100}}], "message": "Fuzzy match found (100%)"}, "f8d7fca0-2a0b-468f-93b1-83773cbd311d": {"passed": true, "score": 100, "matches": ["<EMAIL>"], "message": "Exact match found"}, "be16e846-fb81-4be7-832e-90aceda870a9": {"passed": true, "score": 100, "matches": [{"data": "JohnD#1234", "score": 100, "breakdown": {"exact": 100, "levenshtein": 100, "wordOverlap": 100, "substring": 100}}], "message": "Fuzzy match found (100%)"}}, "verificationScore": 100, "verificationPassed": true, "status": "approved", "assignedRoles": [], "reviewedBy": null, "reviewNotes": "", "submittedAt": 1753040629484, "processedAt": 1753040629491, "updatedAt": 1753040629492, "processingNotes": "Verification passed (100.0%); 0 role(s) assigned; Auto-approved"}, "f935cfe3-127a-477e-b711-0f7dac526006": {"id": "f935cfe3-127a-477e-b711-0f7dac526006", "configId": "473bc4a8-2f73-4e6d-88aa-0965fdc341f2", "guildId": "demo_guild_12345", "userId": "demo_user_67890", "username": "DemoUser#1234", "answers": {"4533bdbb-cb8f-40df-9c2e-7d120f5da7f2": "alpha", "0f60d86b-6b91-4daf-b69c-d3d770f5e158": "advanced", "93350486-6267-4999-b123-7ceeefeb6bc9": "weekdays,evenings"}, "verificationResults": {}, "verificationScore": 0, "verificationPassed": true, "status": "approved", "assignedRoles": [{"roleId": "team_alpha_456", "roleName": "Team Alpha", "ruleName": "Alpha Team Assignment", "score": 100}, {"roleId": "experienced_345", "roleName": "Experienced", "ruleName": "Experienced Member", "score": 100}], "reviewedBy": null, "reviewNotes": "", "submittedAt": 1753040629495, "processedAt": 1753040629501, "updatedAt": 1753040629502, "processingNotes": "Verification disabled; 2 role(s) assigned; Auto-approved; Role application failed: Cannot read properties of undefined (reading 'id')"}}}