/**
 * Form System Demo
 * Demonstrates the capabilities of the advanced form application system
 */

const { FormConfigModels, QUESTION_TYPES, VERIFICATION_CRITERIA, ROLE_ASSIGNMENT_CONDITIONS } = require('../utils/formConfigModels');
const formApplicationStorage = require('../utils/formApplicationStorage');
const formVerificationEngine = require('../utils/formVerificationEngine');
const roleAssignmentEngine = require('../utils/roleAssignmentEngine');
const applicationProcessor = require('../utils/applicationProcessor');

class FormSystemDemo {
    constructor() {
        this.demoGuildId = 'demo_guild_12345';
        this.demoUserId = 'demo_user_67890';
    }

    /**
     * Run the complete demo
     */
    async runDemo() {
        console.log('🚀 Advanced Discord Bot Form Application System Demo');
        console.log('====================================================\n');

        try {
            await this.initializeSystem();
            await this.createSampleVerificationData();
            await this.createMemberApplicationForm();
            await this.createTeamSelectionForm();
            await this.simulateApplicationSubmissions();
            await this.demonstrateAdminFeatures();
            await this.showSystemStatistics();

            console.log('\n🎉 Demo completed successfully!');
            console.log('\nThe form application system is ready for production use.');
            console.log('Use /setup in Discord to configure your own forms.');

        } catch (error) {
            console.error('❌ Demo failed:', error);
        }
    }

    /**
     * Initialize the system
     */
    async initializeSystem() {
        console.log('🔧 Initializing Form Application System...');
        
        await formApplicationStorage.initializeStorage();
        
        console.log('✅ Storage system initialized');
        console.log('✅ Verification engine ready');
        console.log('✅ Role assignment engine ready');
        console.log('✅ Application processor ready\n');
    }

    /**
     * Create sample verification data
     */
    async createSampleVerificationData() {
        console.log('📊 Creating Sample Verification Data...');

        const memberData = [
            { name: 'John Doe', email: '<EMAIL>', discord: 'JohnD#1234', member_id: 'M001' },
            { name: 'Jane Smith', email: '<EMAIL>', discord: 'JaneS#5678', member_id: 'M002' },
            { name: 'Bob Johnson', email: '<EMAIL>', discord: 'BobJ#9012', member_id: 'M003' },
            { name: 'Alice Brown', email: '<EMAIL>', discord: 'AliceB#3456', member_id: 'M004' },
            { name: 'Charlie Wilson', email: '<EMAIL>', discord: 'CharlieW#7890', member_id: 'M005' }
        ];

        await formApplicationStorage.saveVerificationData(this.demoGuildId, 'member_database', memberData);
        
        console.log(`✅ Created verification database with ${memberData.length} member records`);
        console.log('   - Names, emails, Discord usernames, and member IDs');
        console.log('   - Ready for fuzzy matching with 70-90% similarity thresholds\n');
    }

    /**
     * Create member application form
     */
    async createMemberApplicationForm() {
        console.log('📝 Creating Member Application Form...');

        // Create questions
        const nameQuestion = FormConfigModels.createQuestion({
            type: QUESTION_TYPES.TEXT,
            label: 'Full Name',
            description: 'Enter your full legal name',
            required: true,
            isVerificationCriteria: true,
            verificationConfig: FormConfigModels.createVerificationConfig({
                type: VERIFICATION_CRITERIA.FUZZY_MATCH,
                threshold: 80,
                sourceField: 'name'
            }),
            order: 1
        });

        const emailQuestion = FormConfigModels.createQuestion({
            type: QUESTION_TYPES.EMAIL,
            label: 'Email Address',
            description: 'Enter your email address for verification',
            required: true,
            isVerificationCriteria: true,
            verificationConfig: FormConfigModels.createVerificationConfig({
                type: VERIFICATION_CRITERIA.EXACT_MATCH,
                sourceField: 'email'
            }),
            order: 2
        });

        const discordQuestion = FormConfigModels.createQuestion({
            type: QUESTION_TYPES.TEXT,
            label: 'Discord Username',
            description: 'Enter your Discord username (e.g., Username#1234)',
            required: true,
            isVerificationCriteria: true,
            verificationConfig: FormConfigModels.createVerificationConfig({
                type: VERIFICATION_CRITERIA.FUZZY_MATCH,
                threshold: 85,
                sourceField: 'discord'
            }),
            order: 3
        });

        const reasonQuestion = FormConfigModels.createQuestion({
            type: QUESTION_TYPES.TEXTAREA,
            label: 'Why do you want to join?',
            description: 'Tell us why you want to become a member (minimum 50 characters)',
            required: true,
            validation: { minLength: 50, maxLength: 500 },
            order: 4
        });

        // Create role assignment rule
        const memberRoleRule = FormConfigModels.createRoleAssignmentRule({
            name: 'Member Role Assignment',
            description: 'Assign Member role to verified applicants',
            condition: ROLE_ASSIGNMENT_CONDITIONS.VERIFICATION_PASSED,
            roleId: 'member_role_123',
            roleName: 'Member',
            priority: 1
        });

        // Create form configuration
        const memberForm = FormConfigModels.createFormConfig({
            guildId: this.demoGuildId,
            name: 'Member Application',
            description: 'Apply to become a verified member of our community. All information will be verified against our member database.',
            questions: [nameQuestion, emailQuestion, discordQuestion, reasonQuestion],
            verificationEnabled: true,
            verificationChannels: ['member_database'],
            similarityThreshold: 80,
            strictMatching: false,
            roleAssignmentRules: [memberRoleRule],
            autoApprove: true,
            requireAdminReview: false,
            sendDMOnApproval: true,
            approvalMessage: 'Welcome to our community! Your membership has been approved and you have been assigned the Member role.',
            targetChannels: ['applications', 'general']
        });

        const configId = await formApplicationStorage.saveFormConfig(this.demoGuildId, memberForm);
        
        console.log('✅ Member Application Form created:');
        console.log(`   - Form ID: ${configId}`);
        console.log(`   - Questions: ${memberForm.questions.length} (3 with verification)`);
        console.log('   - Verification: Enabled with 80% threshold');
        console.log('   - Processing: Auto-approve verified applications');
        console.log('   - Role Assignment: Member role for verified users\n');

        return configId;
    }

    /**
     * Create team selection form
     */
    async createTeamSelectionForm() {
        console.log('🏆 Creating Team Selection Form...');

        // Create questions
        const teamQuestion = FormConfigModels.createQuestion({
            type: QUESTION_TYPES.DROPDOWN,
            label: 'Preferred Team',
            description: 'Select your preferred team assignment',
            required: true,
            options: [
                { value: 'alpha', label: 'Team Alpha - Development' },
                { value: 'beta', label: 'Team Beta - Design' },
                { value: 'gamma', label: 'Team Gamma - Marketing' }
            ],
            order: 1
        });

        const experienceQuestion = FormConfigModels.createQuestion({
            type: QUESTION_TYPES.MULTIPLE_CHOICE,
            label: 'Experience Level',
            description: 'What is your experience level in your chosen field?',
            required: true,
            options: [
                { value: 'beginner', label: 'Beginner (0-1 years)' },
                { value: 'intermediate', label: 'Intermediate (2-5 years)' },
                { value: 'advanced', label: 'Advanced (5+ years)' }
            ],
            order: 2
        });

        const availabilityQuestion = FormConfigModels.createQuestion({
            type: QUESTION_TYPES.CHECKBOX,
            label: 'Availability',
            description: 'When are you typically available? (Select all that apply)',
            required: true,
            options: [
                { value: 'weekdays', label: 'Weekdays (Mon-Fri)' },
                { value: 'weekends', label: 'Weekends (Sat-Sun)' },
                { value: 'evenings', label: 'Evenings (6PM-10PM)' },
                { value: 'mornings', label: 'Mornings (6AM-12PM)' }
            ],
            order: 3
        });

        // Create role assignment rules
        const alphaTeamRule = FormConfigModels.createRoleAssignmentRule({
            name: 'Alpha Team Assignment',
            condition: ROLE_ASSIGNMENT_CONDITIONS.ANSWER_EQUALS,
            questionId: teamQuestion.id,
            expectedValue: 'alpha',
            roleId: 'team_alpha_456',
            roleName: 'Team Alpha',
            priority: 2
        });

        const betaTeamRule = FormConfigModels.createRoleAssignmentRule({
            name: 'Beta Team Assignment',
            condition: ROLE_ASSIGNMENT_CONDITIONS.ANSWER_EQUALS,
            questionId: teamQuestion.id,
            expectedValue: 'beta',
            roleId: 'team_beta_789',
            roleName: 'Team Beta',
            priority: 2
        });

        const gammaTeamRule = FormConfigModels.createRoleAssignmentRule({
            name: 'Gamma Team Assignment',
            condition: ROLE_ASSIGNMENT_CONDITIONS.ANSWER_EQUALS,
            questionId: teamQuestion.id,
            expectedValue: 'gamma',
            roleId: 'team_gamma_012',
            roleName: 'Team Gamma',
            priority: 2
        });

        const experiencedRule = FormConfigModels.createRoleAssignmentRule({
            name: 'Experienced Member',
            condition: ROLE_ASSIGNMENT_CONDITIONS.ANSWER_EQUALS,
            questionId: experienceQuestion.id,
            expectedValue: 'advanced',
            roleId: 'experienced_345',
            roleName: 'Experienced',
            priority: 1
        });

        // Create form configuration
        const teamForm = FormConfigModels.createFormConfig({
            guildId: this.demoGuildId,
            name: 'Team Selection',
            description: 'Select your team assignment and get the appropriate roles. This form demonstrates conditional role assignment based on multiple answers.',
            questions: [teamQuestion, experienceQuestion, availabilityQuestion],
            verificationEnabled: false,
            roleAssignmentRules: [alphaTeamRule, betaTeamRule, gammaTeamRule, experiencedRule],
            maxRolesPerUser: 3,
            autoApprove: true,
            requireAdminReview: false,
            sendDMOnApproval: true,
            approvalMessage: 'Your team assignment has been processed! Check your roles to see your new team assignment.',
            targetChannels: ['team-selection']
        });

        const configId = await formApplicationStorage.saveFormConfig(this.demoGuildId, teamForm);
        
        console.log('✅ Team Selection Form created:');
        console.log(`   - Form ID: ${configId}`);
        console.log(`   - Questions: ${teamForm.questions.length} (dropdown, multiple choice, checkbox)`);
        console.log('   - Verification: Disabled (no verification needed)');
        console.log('   - Processing: Auto-approve all applications');
        console.log('   - Role Assignment: 4 conditional rules, max 3 roles per user\n');

        return configId;
    }

    /**
     * Simulate application submissions
     */
    async simulateApplicationSubmissions() {
        console.log('📋 Simulating Application Submissions...');

        // Get form configurations
        const guildConfigs = await formApplicationStorage.getGuildFormConfigs(this.demoGuildId);
        const memberForm = Object.values(guildConfigs).find(config => config.name === 'Member Application');
        const teamForm = Object.values(guildConfigs).find(config => config.name === 'Team Selection');

        // Mock guild and user objects
        const mockGuild = {
            id: this.demoGuildId,
            name: 'Demo Guild',
            client: { users: { fetch: async () => ({ send: async () => {} }) } },
            channels: { cache: new Map() },
            members: { fetch: async () => ({ roles: { cache: new Map(), add: async () => {}, remove: async () => {} } }) },
            roles: { cache: new Map([
                ['member_role_123', { id: 'member_role_123', name: 'Member' }],
                ['team_alpha_456', { id: 'team_alpha_456', name: 'Team Alpha' }],
                ['experienced_345', { id: 'experienced_345', name: 'Experienced' }]
            ]) }
        };

        const mockUser = { id: this.demoUserId, tag: 'DemoUser#1234' };

        // Simulate member application (should pass verification)
        console.log('   📝 Submitting Member Application (John Doe)...');
        const memberAnswers = {};
        memberAnswers[memberForm.questions[0].id] = 'John Doe'; // Exact match in verification data
        memberAnswers[memberForm.questions[1].id] = '<EMAIL>'; // Exact match
        memberAnswers[memberForm.questions[2].id] = 'JohnD#1234'; // Exact match
        memberAnswers[memberForm.questions[3].id] = 'I want to join because I am passionate about this community and believe I can contribute valuable insights and help others grow.';

        const memberResult = await applicationProcessor.submitApplication(memberAnswers, memberForm, mockUser, mockGuild);
        
        if (memberResult.success) {
            console.log('   ✅ Member application processed successfully');
            console.log(`      - Status: ${memberResult.processingResult.status}`);
            console.log(`      - Verification: ${memberResult.application.verificationPassed ? 'PASSED' : 'FAILED'} (${memberResult.application.verificationScore?.toFixed(1) || 0}%)`);
            console.log(`      - Roles: ${memberResult.processingResult.roleAssignments?.length || 0} assigned`);
        } else {
            console.log('   ❌ Member application failed:', memberResult.error);
        }

        // Simulate team selection application
        console.log('   🏆 Submitting Team Selection Application...');
        const teamAnswers = {};
        teamAnswers[teamForm.questions[0].id] = 'alpha'; // Team Alpha
        teamAnswers[teamForm.questions[1].id] = 'advanced'; // Advanced experience
        teamAnswers[teamForm.questions[2].id] = 'weekdays,evenings'; // Multiple availability options

        const teamResult = await applicationProcessor.submitApplication(teamAnswers, teamForm, mockUser, mockGuild);
        
        if (teamResult.success) {
            console.log('   ✅ Team selection processed successfully');
            console.log(`      - Status: ${teamResult.processingResult.status}`);
            console.log(`      - Roles: ${teamResult.processingResult.roleAssignments?.length || 0} assigned`);
            if (teamResult.processingResult.roleAssignments) {
                teamResult.processingResult.roleAssignments.forEach(role => {
                    console.log(`        • ${role.roleName} (${role.ruleName})`);
                });
            }
        } else {
            console.log('   ❌ Team selection failed:', teamResult.error);
        }

        console.log();
    }

    /**
     * Demonstrate admin features
     */
    async demonstrateAdminFeatures() {
        console.log('🛡️ Demonstrating Admin Features...');

        // Get applications
        const applications = await formApplicationStorage.getApplicationsByGuild(this.demoGuildId);
        console.log(`   📊 Total applications in system: ${applications.length}`);

        // Show application details
        applications.forEach((app, index) => {
            console.log(`   ${index + 1}. Application ${app.id.substring(0, 8)}`);
            console.log(`      - User: ${app.username}`);
            console.log(`      - Status: ${app.status}`);
            console.log(`      - Verification: ${app.verificationPassed ? 'PASSED' : 'FAILED'}`);
            console.log(`      - Submitted: ${new Date(app.submittedAt).toLocaleString()}`);
        });

        // Get audit logs
        const auditLogs = await formApplicationStorage.getAuditLogs({ guildId: this.demoGuildId, limit: 5 });
        console.log(`   📋 Recent audit log entries: ${auditLogs.length}`);
        
        auditLogs.forEach((log, index) => {
            console.log(`   ${index + 1}. ${log.action} - ${new Date(log.timestamp).toLocaleString()}`);
        });

        console.log();
    }

    /**
     * Show system statistics
     */
    async showSystemStatistics() {
        console.log('📈 System Statistics...');

        const storageStats = await formApplicationStorage.getStorageStats();
        
        console.log('   📊 Storage Statistics:');
        console.log(`      - Guilds: ${storageStats.guilds}`);
        console.log(`      - Form Configurations: ${storageStats.formConfigs}`);
        console.log(`      - Applications: ${storageStats.applications}`);
        console.log(`      - Verification Channels: ${storageStats.verificationChannels}`);
        console.log(`      - Audit Log Entries: ${storageStats.auditLogEntries}`);
        
        console.log('   💾 Cache Statistics:');
        console.log(`      - Cached Configs: ${storageStats.cacheSize.configs}`);
        console.log(`      - Cached Applications: ${storageStats.cacheSize.applications}`);
        console.log(`      - Cached Verification Data: ${storageStats.cacheSize.verification}`);

        console.log();
    }
}

// Export for use
module.exports = FormSystemDemo;

// Run demo if this file is executed directly
if (require.main === module) {
    const demo = new FormSystemDemo();
    demo.runDemo().catch(console.error);
}
