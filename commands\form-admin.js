/**
 * Form Admin Command
 * Administrative interface for managing form applications
 */

const {
    SlashCommandBuilder,
    PermissionFlagsBits,
    MessageFlags,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder
} = require('discord.js');

const formApplicationStorage = require('../utils/formApplicationStorage');
const adminReviewSystem = require('../utils/adminReviewSystem');
const { APPLICATION_STATUS } = require('../utils/formConfigModels');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('form-admin')
        .setDescription('Administrative interface for managing form applications')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .addSubcommand(subcommand =>
            subcommand
                .setName('review')
                .setDescription('Review and manage submitted applications')
                .addStringOption(option =>
                    option.setName('status')
                        .setDescription('Filter by application status')
                        .setRequired(false)
                        .addChoices(
                            { name: 'Pending', value: APPLICATION_STATUS.PENDING },
                            { name: 'Under Review', value: APPLICATION_STATUS.UNDER_REVIEW },
                            { name: 'Approved', value: APPLICATION_STATUS.APPROVED },
                            { name: 'Rejected', value: APPLICATION_STATUS.REJECTED },
                            { name: 'Requires Verification', value: APPLICATION_STATUS.REQUIRES_VERIFICATION }
                        ))
                .addUserOption(option =>
                    option.setName('user')
                        .setDescription('Filter by specific user')
                        .setRequired(false))
                .addStringOption(option =>
                    option.setName('form')
                        .setDescription('Filter by form name')
                        .setRequired(false)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('stats')
                .setDescription('View application statistics and analytics'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('export')
                .setDescription('Export application data')
                .addStringOption(option =>
                    option.setName('format')
                        .setDescription('Export format')
                        .setRequired(false)
                        .addChoices(
                            { name: 'CSV', value: 'csv' },
                            { name: 'JSON', value: 'json' }
                        )))
        .addSubcommand(subcommand =>
            subcommand
                .setName('cleanup')
                .setDescription('Clean up old applications')
                .addIntegerOption(option =>
                    option.setName('days')
                        .setDescription('Delete applications older than this many days')
                        .setRequired(true)
                        .setMinValue(1)
                        .setMaxValue(365))),

    async execute(interaction) {
        try {
            const subcommand = interaction.options.getSubcommand();

            switch (subcommand) {
                case 'review':
                    await this.handleReview(interaction);
                    break;
                case 'stats':
                    await this.handleStats(interaction);
                    break;
                case 'export':
                    await this.handleExport(interaction);
                    break;
                case 'cleanup':
                    await this.handleCleanup(interaction);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Unknown subcommand.',
                        flags: MessageFlags.Ephemeral
                    });
            }

        } catch (error) {
            console.error('[FORM_ADMIN] Error in form-admin command:', error);
            await interaction.reply({
                content: `❌ **Error**\nFailed to process admin command: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Handle review subcommand
     */
    async handleReview(interaction) {
        try {
            const status = interaction.options.getString('status');
            const user = interaction.options.getUser('user');
            const formName = interaction.options.getString('form');

            // Build filters
            const filters = {};
            if (status) filters.status = status;
            if (user) filters.userId = user.id;

            // If form name is specified, find the config ID
            if (formName) {
                const guildConfigs = await formApplicationStorage.getGuildFormConfigs(interaction.guild.id);
                const matchingConfig = Object.values(guildConfigs).find(config => 
                    config.name.toLowerCase().includes(formName.toLowerCase())
                );
                
                if (matchingConfig) {
                    filters.configId = matchingConfig.id;
                } else {
                    return await interaction.reply({
                        content: `❌ No form found matching "${formName}".`,
                        flags: MessageFlags.Ephemeral
                    });
                }
            }

            // Create admin dashboard
            await adminReviewSystem.createAdminDashboard(interaction, filters);

        } catch (error) {
            console.error('[FORM_ADMIN] Error handling review:', error);
            throw error;
        }
    },

    /**
     * Handle stats subcommand
     */
    async handleStats(interaction) {
        try {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });

            const guildId = interaction.guild.id;
            const applications = await formApplicationStorage.getApplicationsByGuild(guildId);
            const guildConfigs = await formApplicationStorage.getGuildFormConfigs(guildId);
            const storageStats = await formApplicationStorage.getStorageStats();

            // Calculate statistics
            const stats = {
                total: applications.length,
                pending: applications.filter(app => app.status === APPLICATION_STATUS.PENDING).length,
                approved: applications.filter(app => app.status === APPLICATION_STATUS.APPROVED).length,
                rejected: applications.filter(app => app.status === APPLICATION_STATUS.REJECTED).length,
                underReview: applications.filter(app => app.status === APPLICATION_STATUS.UNDER_REVIEW).length,
                requiresVerification: applications.filter(app => app.status === APPLICATION_STATUS.REQUIRES_VERIFICATION).length
            };

            // Calculate time-based statistics
            const now = Date.now();
            const oneDayAgo = now - (24 * 60 * 60 * 1000);
            const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000);
            const oneMonthAgo = now - (30 * 24 * 60 * 60 * 1000);

            const recentStats = {
                today: applications.filter(app => app.submittedAt >= oneDayAgo).length,
                thisWeek: applications.filter(app => app.submittedAt >= oneWeekAgo).length,
                thisMonth: applications.filter(app => app.submittedAt >= oneMonthAgo).length
            };

            // Form-specific statistics
            const formStats = Object.values(guildConfigs).map(config => {
                const formApps = applications.filter(app => app.configId === config.id);
                return {
                    name: config.name,
                    total: formApps.length,
                    approved: formApps.filter(app => app.status === APPLICATION_STATUS.APPROVED).length,
                    pending: formApps.filter(app => app.status === APPLICATION_STATUS.PENDING).length
                };
            }).filter(stat => stat.total > 0);

            const embed = new EmbedBuilder()
                .setTitle('📊 Application Statistics')
                .setDescription(`Statistics for ${interaction.guild.name}`)
                .setColor(0x3498DB)
                .addFields(
                    {
                        name: '📋 Total Applications',
                        value: stats.total.toString(),
                        inline: true
                    },
                    {
                        name: '✅ Approved',
                        value: `${stats.approved} (${stats.total > 0 ? Math.round((stats.approved / stats.total) * 100) : 0}%)`,
                        inline: true
                    },
                    {
                        name: '❌ Rejected',
                        value: `${stats.rejected} (${stats.total > 0 ? Math.round((stats.rejected / stats.total) * 100) : 0}%)`,
                        inline: true
                    },
                    {
                        name: '⏳ Pending',
                        value: stats.pending.toString(),
                        inline: true
                    },
                    {
                        name: '👥 Under Review',
                        value: stats.underReview.toString(),
                        inline: true
                    },
                    {
                        name: '🔍 Requires Verification',
                        value: stats.requiresVerification.toString(),
                        inline: true
                    },
                    {
                        name: '📅 Recent Activity',
                        value: `• Today: ${recentStats.today}\n• This Week: ${recentStats.thisWeek}\n• This Month: ${recentStats.thisMonth}`,
                        inline: false
                    }
                );

            // Add form-specific stats
            if (formStats.length > 0) {
                const formStatsText = formStats
                    .slice(0, 5) // Limit to 5 forms to prevent embed size issues
                    .map(stat => `• **${stat.name}:** ${stat.total} total (${stat.approved} approved, ${stat.pending} pending)`)
                    .join('\n');
                
                embed.addFields({
                    name: '📝 Form Statistics',
                    value: formStatsText + (formStats.length > 5 ? `\n• And ${formStats.length - 5} more forms...` : ''),
                    inline: false
                });
            }

            // Add system statistics
            embed.addFields({
                name: '🔧 System Statistics',
                value: `• Active Forms: ${Object.keys(guildConfigs).length}\n` +
                       `• Storage Cache: ${storageStats.cacheSize.applications} applications cached\n` +
                       `• Audit Log Entries: ${storageStats.auditLogEntries}`,
                inline: false
            });

            embed.setFooter({ text: 'Form Application System • Statistics' })
                .setTimestamp();

            await interaction.followUp({ embeds: [embed] });

        } catch (error) {
            console.error('[FORM_ADMIN] Error handling stats:', error);
            await interaction.followUp({
                content: `❌ Error generating statistics: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Handle export subcommand
     */
    async handleExport(interaction) {
        try {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });

            const format = interaction.options.getString('format') || 'csv';
            const guildId = interaction.guild.id;
            const applications = await formApplicationStorage.getApplicationsByGuild(guildId);

            if (applications.length === 0) {
                return await interaction.followUp({
                    content: '❌ No applications found to export.',
                    flags: MessageFlags.Ephemeral
                });
            }

            let exportData;
            let filename;
            let contentType;

            if (format === 'csv') {
                exportData = this.generateCSV(applications);
                filename = `applications_${guildId}_${Date.now()}.csv`;
                contentType = 'text/csv';
            } else {
                exportData = JSON.stringify(applications, null, 2);
                filename = `applications_${guildId}_${Date.now()}.json`;
                contentType = 'application/json';
            }

            // Create a buffer from the export data
            const buffer = Buffer.from(exportData, 'utf8');

            // Send the file
            await interaction.followUp({
                content: `📤 **Export Complete**\n\nExported ${applications.length} applications in ${format.toUpperCase()} format.`,
                files: [{
                    attachment: buffer,
                    name: filename,
                    description: `Application export for ${interaction.guild.name}`
                }],
                flags: MessageFlags.Ephemeral
            });

            // Log the export
            await formApplicationStorage.addAuditLog({
                action: 'applications_exported',
                guildId,
                userId: interaction.user.id,
                details: {
                    format,
                    applicationCount: applications.length,
                    filename
                }
            });

        } catch (error) {
            console.error('[FORM_ADMIN] Error handling export:', error);
            await interaction.followUp({
                content: `❌ Error exporting data: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Handle cleanup subcommand
     */
    async handleCleanup(interaction) {
        try {
            const days = interaction.options.getInteger('days');
            const cutoffDate = Date.now() - (days * 24 * 60 * 60 * 1000);

            await interaction.reply({
                content: `⚠️ **Cleanup Confirmation**\n\nThis will permanently delete all applications older than ${days} days (submitted before <t:${Math.floor(cutoffDate / 1000)}:F>).\n\n**This action cannot be undone!**\n\nAre you sure you want to proceed?`,
                components: [
                    new ActionRowBuilder()
                        .addComponents(
                            new ButtonBuilder()
                                .setCustomId(`cleanup_confirm_${days}`)
                                .setLabel('Confirm Cleanup')
                                .setStyle(ButtonStyle.Danger)
                                .setEmoji('🗑️'),
                            new ButtonBuilder()
                                .setCustomId('cleanup_cancel')
                                .setLabel('Cancel')
                                .setStyle(ButtonStyle.Secondary)
                                .setEmoji('❌')
                        )
                ],
                flags: MessageFlags.Ephemeral
            });

        } catch (error) {
            console.error('[FORM_ADMIN] Error handling cleanup:', error);
            throw error;
        }
    },

    /**
     * Generate CSV export data
     */
    generateCSV(applications) {
        const headers = [
            'Application ID',
            'User ID',
            'Username',
            'Status',
            'Submitted At',
            'Processed At',
            'Verification Passed',
            'Verification Score',
            'Assigned Roles',
            'Processing Notes'
        ];

        const rows = applications.map(app => [
            app.id,
            app.userId,
            app.username,
            app.status,
            new Date(app.submittedAt).toISOString(),
            app.processedAt ? new Date(app.processedAt).toISOString() : '',
            app.verificationPassed ? 'Yes' : 'No',
            app.verificationScore || 0,
            app.assignedRoles ? app.assignedRoles.map(r => r.roleName).join('; ') : '',
            app.processingNotes || ''
        ]);

        return [headers, ...rows]
            .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
            .join('\n');
    }
};

// Export handler functions for use in main bot file
module.exports.handleCleanupConfirm = async function(interaction) {
    try {
        const days = parseInt(interaction.customId.split('_')[2]);
        const cutoffDate = Date.now() - (days * 24 * 60 * 60 * 1000);
        
        await interaction.update({
            content: '⏳ Processing cleanup...',
            components: []
        });

        const applications = await formApplicationStorage.getApplicationsByGuild(interaction.guild.id, {
            dateTo: cutoffDate
        });

        // This would implement the actual cleanup logic
        // For now, just show what would be deleted
        await interaction.followUp({
            content: `✅ **Cleanup Complete**\n\nWould have deleted ${applications.length} applications older than ${days} days.\n\n*Note: Actual deletion not implemented in this demo.*`,
            flags: MessageFlags.Ephemeral
        });

    } catch (error) {
        console.error('[FORM_ADMIN] Error in cleanup confirm:', error);
        await interaction.followUp({
            content: `❌ Error during cleanup: ${error.message}`,
            flags: MessageFlags.Ephemeral
        });
    }
};

module.exports.handleCleanupCancel = async function(interaction) {
    await interaction.update({
        content: '❌ Cleanup cancelled.',
        components: []
    });
};
