/**
 * Form Renderer
 * Handles rendering of dynamic forms based on configuration
 */

const {
    <PERSON>bed<PERSON><PERSON>er,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder
} = require('discord.js');

const { QUESTION_TYPES } = require('./formConfigModels');

class FormRenderer {
    constructor() {
        this.activeForms = new Map(); // Store active form sessions
        this.setupCleanupInterval();
    }

    /**
     * Setup cleanup interval for expired form sessions
     */
    setupCleanupInterval() {
        setInterval(() => {
            const now = Date.now();
            const maxAge = 30 * 60 * 1000; // 30 minutes
            
            for (const [sessionId, session] of this.activeForms.entries()) {
                if (now - session.createdAt > maxAge) {
                    this.activeForms.delete(sessionId);
                    console.log(`[FORM_RENDERER] Cleaned up expired form session: ${sessionId}`);
                }
            }
        }, 5 * 60 * 1000); // Check every 5 minutes
    }

    /**
     * Render form as an embed with interactive components
     */
    async renderForm(formConfig, interaction, startFromQuestion = 0) {
        try {
            const sessionId = `${interaction.user.id}_${formConfig.id}_${Date.now()}`;
            
            // Create form session
            const session = {
                sessionId,
                userId: interaction.user.id,
                guildId: interaction.guild.id,
                configId: formConfig.id,
                formConfig,
                currentQuestionIndex: startFromQuestion,
                answers: {},
                createdAt: Date.now(),
                lastInteraction: Date.now()
            };
            
            this.activeForms.set(sessionId, session);
            
            // Render the current question
            return await this.renderCurrentQuestion(session);
            
        } catch (error) {
            console.error('[FORM_RENDERER] Error rendering form:', error);
            throw error;
        }
    }

    /**
     * Render the current question in the form
     */
    async renderCurrentQuestion(session) {
        const { formConfig, currentQuestionIndex, answers } = session;
        const questions = formConfig.questions.sort((a, b) => a.order - b.order);
        
        if (currentQuestionIndex >= questions.length) {
            // Form is complete, show summary
            return this.renderFormSummary(session);
        }
        
        const currentQuestion = questions[currentQuestionIndex];
        const progress = `${currentQuestionIndex + 1}/${questions.length}`;
        
        const embed = new EmbedBuilder()
            .setTitle(`📝 ${formConfig.name}`)
            .setDescription(
                formConfig.description ? `${formConfig.description}\n\n` : '' +
                `**Question ${progress}**`
            )
            .setColor(0x3498DB)
            .addFields({
                name: currentQuestion.label + (currentQuestion.required ? ' *' : ''),
                value: currentQuestion.description || 'Please provide your answer below.',
                inline: false
            })
            .setFooter({ 
                text: `Progress: ${progress} • ${currentQuestion.required ? 'Required' : 'Optional'}${currentQuestion.isVerificationCriteria ? ' • Verification Required' : ''}`
            })
            .setTimestamp();

        // Add current answer if exists
        const currentAnswer = answers[currentQuestion.id];
        if (currentAnswer) {
            embed.addFields({
                name: 'Current Answer',
                value: `\`${currentAnswer}\``,
                inline: false
            });
        }

        const components = await this.createQuestionComponents(session, currentQuestion);
        
        return {
            embeds: [embed],
            components,
            sessionId: session.sessionId
        };
    }

    /**
     * Create interactive components for a question
     */
    async createQuestionComponents(session, question) {
        const components = [];
        
        switch (question.type) {
            case QUESTION_TYPES.DROPDOWN:
                components.push(this.createDropdownComponent(session, question));
                break;
                
            case QUESTION_TYPES.MULTIPLE_CHOICE:
                components.push(this.createMultipleChoiceComponent(session, question));
                break;
                
            case QUESTION_TYPES.CHECKBOX:
                components.push(this.createCheckboxComponent(session, question));
                break;
                
            default:
                // For text-based inputs, use a modal
                components.push(this.createTextInputComponent(session, question));
                break;
        }
        
        // Add navigation buttons
        components.push(this.createNavigationComponent(session));
        
        return components;
    }

    /**
     * Create dropdown component
     */
    createDropdownComponent(session, question) {
        const options = question.options.slice(0, 25).map(option => 
            new StringSelectMenuOptionBuilder()
                .setLabel(option.label || option.value)
                .setValue(option.value)
                .setDescription(option.description || null)
        );
        
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`form_dropdown_${session.sessionId}_${question.id}`)
            .setPlaceholder(question.placeholder || 'Select an option...')
            .addOptions(options);
        
        return new ActionRowBuilder().addComponents(selectMenu);
    }

    /**
     * Create multiple choice component (buttons)
     */
    createMultipleChoiceComponent(session, question) {
        const buttons = question.options.slice(0, 5).map(option => 
            new ButtonBuilder()
                .setCustomId(`form_choice_${session.sessionId}_${question.id}_${option.value}`)
                .setLabel(option.label || option.value)
                .setStyle(ButtonStyle.Secondary)
        );
        
        return new ActionRowBuilder().addComponents(buttons);
    }

    /**
     * Create checkbox component (multiple buttons)
     */
    createCheckboxComponent(session, question) {
        const currentAnswers = session.answers[question.id] ? 
            session.answers[question.id].split(',') : [];
        
        const buttons = question.options.slice(0, 5).map(option => {
            const isSelected = currentAnswers.includes(option.value);
            return new ButtonBuilder()
                .setCustomId(`form_checkbox_${session.sessionId}_${question.id}_${option.value}`)
                .setLabel((isSelected ? '✅ ' : '') + (option.label || option.value))
                .setStyle(isSelected ? ButtonStyle.Success : ButtonStyle.Secondary);
        });
        
        return new ActionRowBuilder().addComponents(buttons);
    }

    /**
     * Create text input component (modal trigger)
     */
    createTextInputComponent(session, question) {
        const button = new ButtonBuilder()
            .setCustomId(`form_text_${session.sessionId}_${question.id}`)
            .setLabel('Enter Answer')
            .setStyle(ButtonStyle.Primary)
            .setEmoji('✏️');
        
        return new ActionRowBuilder().addComponents(button);
    }

    /**
     * Create navigation component
     */
    createNavigationComponent(session) {
        const buttons = [];
        
        // Previous button
        if (session.currentQuestionIndex > 0) {
            buttons.push(
                new ButtonBuilder()
                    .setCustomId(`form_prev_${session.sessionId}`)
                    .setLabel('Previous')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️')
            );
        }
        
        // Next/Submit button
        const isLastQuestion = session.currentQuestionIndex >= session.formConfig.questions.length - 1;
        buttons.push(
            new ButtonBuilder()
                .setCustomId(`form_next_${session.sessionId}`)
                .setLabel(isLastQuestion ? 'Submit' : 'Next')
                .setStyle(isLastQuestion ? ButtonStyle.Success : ButtonStyle.Primary)
                .setEmoji(isLastQuestion ? '📤' : '➡️')
        );
        
        // Cancel button
        buttons.push(
            new ButtonBuilder()
                .setCustomId(`form_cancel_${session.sessionId}`)
                .setLabel('Cancel')
                .setStyle(ButtonStyle.Danger)
                .setEmoji('❌')
        );
        
        return new ActionRowBuilder().addComponents(buttons);
    }

    /**
     * Create modal for text input questions
     */
    createTextInputModal(session, question) {
        const modal = new ModalBuilder()
            .setCustomId(`form_modal_${session.sessionId}_${question.id}`)
            .setTitle(`${question.label}`);

        let textInputStyle = TextInputStyle.Short;
        let maxLength = 1000;
        
        // Determine input style based on question type
        switch (question.type) {
            case QUESTION_TYPES.TEXTAREA:
                textInputStyle = TextInputStyle.Paragraph;
                maxLength = 4000;
                break;
            case QUESTION_TYPES.EMAIL:
                maxLength = 100;
                break;
            case QUESTION_TYPES.PHONE:
                maxLength = 20;
                break;
            case QUESTION_TYPES.NUMBER:
                maxLength = 20;
                break;
        }

        const textInput = new TextInputBuilder()
            .setCustomId('answer')
            .setLabel(question.label)
            .setStyle(textInputStyle)
            .setRequired(question.required)
            .setMaxLength(maxLength);

        if (question.placeholder) {
            textInput.setPlaceholder(question.placeholder);
        }

        if (question.validation?.minLength) {
            textInput.setMinLength(question.validation.minLength);
        }

        if (question.validation?.maxLength) {
            textInput.setMaxLength(Math.min(question.validation.maxLength, maxLength));
        }

        // Set current value if exists
        const currentAnswer = session.answers[question.id];
        if (currentAnswer) {
            textInput.setValue(currentAnswer);
        }

        modal.addComponents(new ActionRowBuilder().addComponents(textInput));
        return modal;
    }

    /**
     * Render form summary before submission
     */
    renderFormSummary(session) {
        const { formConfig, answers } = session;
        const questions = formConfig.questions.sort((a, b) => a.order - b.order);
        
        const embed = new EmbedBuilder()
            .setTitle(`📋 ${formConfig.name} - Review Your Answers`)
            .setDescription(
                'Please review your answers before submitting. You can go back to edit any answer if needed.\n\n' +
                '**Your Responses:**'
            )
            .setColor(0x27AE60);

        // Add each answer
        questions.forEach((question, index) => {
            const answer = answers[question.id] || '*No answer provided*';
            embed.addFields({
                name: `${index + 1}. ${question.label}${question.required ? ' *' : ''}`,
                value: `\`${answer}\``,
                inline: false
            });
        });

        embed.setFooter({ 
            text: `${questions.length} questions • Click Submit to complete your application`
        });

        // Create final submission buttons
        const submitRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`form_submit_${session.sessionId}`)
                    .setLabel('Submit Application')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('📤'),
                new ButtonBuilder()
                    .setCustomId(`form_edit_${session.sessionId}`)
                    .setLabel('Edit Answers')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('✏️'),
                new ButtonBuilder()
                    .setCustomId(`form_cancel_${session.sessionId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        return {
            embeds: [embed],
            components: [submitRow],
            sessionId: session.sessionId
        };
    }

    /**
     * Get form session by ID
     */
    getFormSession(sessionId) {
        return this.activeForms.get(sessionId);
    }

    /**
     * Update form session
     */
    updateFormSession(sessionId, updates) {
        const session = this.activeForms.get(sessionId);
        if (session) {
            Object.assign(session, updates, { lastInteraction: Date.now() });
            this.activeForms.set(sessionId, session);
        }
        return session;
    }

    /**
     * Delete form session
     */
    deleteFormSession(sessionId) {
        return this.activeForms.delete(sessionId);
    }
}

// Create singleton instance
const formRenderer = new FormRenderer();

module.exports = formRenderer;
