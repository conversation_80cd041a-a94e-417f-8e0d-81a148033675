/**
 * Role Assignment Engine
 * Handles conditional role assignment based on form responses
 */

const { ROLE_ASSIGNMENT_CONDITIONS } = require('./formConfigModels');
const formApplicationStorage = require('./formApplicationStorage');

class RoleAssignmentEngine {
    constructor() {
        this.assignmentCache = new Map();
        this.setupCleanupInterval();
    }

    /**
     * Setup cleanup interval for cache
     */
    setupCleanupInterval() {
        setInterval(() => {
            this.assignmentCache.clear();
            console.log('[ROLE_ASSIGNMENT] Cleared assignment cache');
        }, 30 * 60 * 1000); // Clear every 30 minutes
    }

    /**
     * Determine role assignments for an application
     */
    async determineRoleAssignments(application, formConfig, guild) {
        try {
            console.log(`[ROLE_ASSIGNMENT] Determining roles for application ${application.id}`);
            
            const assignments = [];
            const matchedRules = [];
            const failedRules = [];

            // Process each role assignment rule
            for (const rule of formConfig.roleAssignmentRules) {
                if (!rule.isActive) {
                    console.log(`[ROLE_ASSIGNMENT] Skipping inactive rule: ${rule.name}`);
                    continue;
                }

                const ruleResult = await this.evaluateRule(rule, application, formConfig, guild);
                
                if (ruleResult.matches) {
                    matchedRules.push({
                        rule,
                        result: ruleResult,
                        priority: rule.priority || 0
                    });
                } else {
                    failedRules.push({
                        rule,
                        result: ruleResult
                    });
                }
            }

            // Sort matched rules by priority (higher priority first)
            matchedRules.sort((a, b) => b.priority - a.priority);

            // Apply role assignments based on matched rules
            for (const { rule, result } of matchedRules) {
                if (assignments.length >= formConfig.maxRolesPerUser) {
                    console.log(`[ROLE_ASSIGNMENT] Max roles per user (${formConfig.maxRolesPerUser}) reached`);
                    break;
                }

                const roleAssignment = await this.createRoleAssignment(rule, result, guild);
                if (roleAssignment) {
                    assignments.push(roleAssignment);
                }
            }

            // If no rules matched and there's a default role, assign it
            if (assignments.length === 0 && formConfig.defaultRole) {
                const defaultRoleAssignment = await this.createDefaultRoleAssignment(formConfig.defaultRole, guild);
                if (defaultRoleAssignment) {
                    assignments.push(defaultRoleAssignment);
                }
            }

            // Log the assignment decision
            await formApplicationStorage.addAuditLog({
                action: 'role_assignment_determined',
                guildId: guild.id,
                userId: application.userId,
                applicationId: application.id,
                details: {
                    matchedRules: matchedRules.length,
                    failedRules: failedRules.length,
                    assignedRoles: assignments.length,
                    roles: assignments.map(a => a.roleName)
                }
            });

            return {
                success: true,
                assignments,
                matchedRules: matchedRules.map(m => m.rule),
                failedRules: failedRules.map(f => f.rule),
                summary: {
                    totalRules: formConfig.roleAssignmentRules.length,
                    matchedRules: matchedRules.length,
                    assignedRoles: assignments.length
                }
            };

        } catch (error) {
            console.error('[ROLE_ASSIGNMENT] Error determining role assignments:', error);
            return {
                success: false,
                error: error.message,
                assignments: [],
                matchedRules: [],
                failedRules: []
            };
        }
    }

    /**
     * Evaluate a single role assignment rule
     */
    async evaluateRule(rule, application, formConfig, guild) {
        try {
            const question = formConfig.questions.find(q => q.id === rule.questionId);
            if (!question) {
                return {
                    matches: false,
                    error: `Question not found: ${rule.questionId}`,
                    details: {}
                };
            }

            const answer = application.answers[rule.questionId];
            if (answer === undefined || answer === null) {
                return {
                    matches: false,
                    error: 'No answer provided for this question',
                    details: { question: question.label }
                };
            }

            // Evaluate based on condition type
            const result = await this.evaluateCondition(rule.condition, answer, rule, application, formConfig);
            
            return {
                matches: result.matches,
                score: result.score || 0,
                details: {
                    question: question.label,
                    answer,
                    condition: rule.condition,
                    expectedValue: rule.expectedValue,
                    ...result.details
                }
            };

        } catch (error) {
            console.error(`[ROLE_ASSIGNMENT] Error evaluating rule ${rule.name}:`, error);
            return {
                matches: false,
                error: error.message,
                details: {}
            };
        }
    }

    /**
     * Evaluate a specific condition
     */
    async evaluateCondition(condition, answer, rule, application, formConfig) {
        const normalizedAnswer = answer.toString().toLowerCase().trim();
        
        switch (condition) {
            case ROLE_ASSIGNMENT_CONDITIONS.ANSWER_EQUALS:
                const expectedValue = rule.expectedValue?.toString().toLowerCase().trim();
                return {
                    matches: normalizedAnswer === expectedValue,
                    score: normalizedAnswer === expectedValue ? 100 : 0,
                    details: { comparedValue: expectedValue }
                };

            case ROLE_ASSIGNMENT_CONDITIONS.ANSWER_CONTAINS:
                const containsValue = rule.expectedValue?.toString().toLowerCase().trim();
                const contains = normalizedAnswer.includes(containsValue);
                return {
                    matches: contains,
                    score: contains ? 75 : 0,
                    details: { searchValue: containsValue }
                };

            case ROLE_ASSIGNMENT_CONDITIONS.ANSWER_IN_LIST:
                const expectedValues = (rule.expectedValues || []).map(v => v.toString().toLowerCase().trim());
                const inList = expectedValues.includes(normalizedAnswer);
                return {
                    matches: inList,
                    score: inList ? 100 : 0,
                    details: { expectedValues: rule.expectedValues }
                };

            case ROLE_ASSIGNMENT_CONDITIONS.MULTIPLE_ANSWERS:
                return await this.evaluateMultipleAnswers(rule, application, formConfig);

            case ROLE_ASSIGNMENT_CONDITIONS.VERIFICATION_PASSED:
                const verificationPassed = application.verificationPassed || false;
                return {
                    matches: verificationPassed,
                    score: verificationPassed ? 100 : 0,
                    details: { verificationScore: application.verificationScore }
                };

            case ROLE_ASSIGNMENT_CONDITIONS.CUSTOM_LOGIC:
                return await this.evaluateCustomLogic(rule, answer, application, formConfig);

            default:
                throw new Error(`Unknown condition type: ${condition}`);
        }
    }

    /**
     * Evaluate multiple answers condition
     */
    async evaluateMultipleAnswers(rule, application, formConfig) {
        try {
            // Custom logic for evaluating multiple answers
            // This could be extended to support complex conditions
            const customLogic = rule.customLogic;
            if (!customLogic || !customLogic.conditions) {
                return { matches: false, score: 0, details: { error: 'No multiple answer conditions defined' } };
            }

            let matchedConditions = 0;
            const totalConditions = customLogic.conditions.length;
            const conditionResults = [];

            for (const condition of customLogic.conditions) {
                const answer = application.answers[condition.questionId];
                const result = await this.evaluateCondition(
                    condition.type,
                    answer,
                    { expectedValue: condition.expectedValue, expectedValues: condition.expectedValues },
                    application,
                    formConfig
                );

                conditionResults.push({
                    questionId: condition.questionId,
                    result
                });

                if (result.matches) {
                    matchedConditions++;
                }
            }

            // Determine if rule matches based on logic type
            const logicType = customLogic.logicType || 'AND'; // AND, OR, MAJORITY
            let matches = false;

            switch (logicType) {
                case 'AND':
                    matches = matchedConditions === totalConditions;
                    break;
                case 'OR':
                    matches = matchedConditions > 0;
                    break;
                case 'MAJORITY':
                    matches = matchedConditions > totalConditions / 2;
                    break;
            }

            const score = totalConditions > 0 ? Math.round((matchedConditions / totalConditions) * 100) : 0;

            return {
                matches,
                score,
                details: {
                    logicType,
                    matchedConditions,
                    totalConditions,
                    conditionResults
                }
            };

        } catch (error) {
            return {
                matches: false,
                score: 0,
                details: { error: error.message }
            };
        }
    }

    /**
     * Evaluate custom logic condition
     */
    async evaluateCustomLogic(rule, answer, application, formConfig) {
        try {
            // This would implement custom JavaScript evaluation
            // For security, this should be sandboxed or use a safe evaluation library
            const customFunction = rule.customLogic?.function;
            if (!customFunction) {
                return { matches: false, score: 0, details: { error: 'No custom function defined' } };
            }

            // For now, return a placeholder
            // In production, implement safe evaluation
            return {
                matches: false,
                score: 0,
                details: { error: 'Custom logic evaluation not implemented yet' }
            };

        } catch (error) {
            return {
                matches: false,
                score: 0,
                details: { error: error.message }
            };
        }
    }

    /**
     * Create role assignment from rule
     */
    async createRoleAssignment(rule, ruleResult, guild) {
        try {
            let role = null;

            // Try to find role by ID first
            if (rule.roleId) {
                role = guild.roles.cache.get(rule.roleId);
            }

            // If not found by ID, try by name
            if (!role && rule.roleName) {
                role = guild.roles.cache.find(r => r.name.toLowerCase() === rule.roleName.toLowerCase());
            }

            if (!role) {
                console.log(`[ROLE_ASSIGNMENT] Role not found for rule ${rule.name}: ${rule.roleName || rule.roleId}`);
                return null;
            }

            return {
                roleId: role.id,
                roleName: role.name,
                ruleName: rule.name,
                ruleId: rule.id,
                score: ruleResult.score,
                priority: rule.priority || 0,
                assignmentReason: `Matched rule: ${rule.name}`,
                details: ruleResult.details
            };

        } catch (error) {
            console.error(`[ROLE_ASSIGNMENT] Error creating role assignment for rule ${rule.name}:`, error);
            return null;
        }
    }

    /**
     * Create default role assignment
     */
    async createDefaultRoleAssignment(defaultRoleId, guild) {
        try {
            const role = guild.roles.cache.get(defaultRoleId);
            if (!role) {
                console.log(`[ROLE_ASSIGNMENT] Default role not found: ${defaultRoleId}`);
                return null;
            }

            return {
                roleId: role.id,
                roleName: role.name,
                ruleName: 'Default Role',
                ruleId: 'default',
                score: 50,
                priority: -1,
                assignmentReason: 'Default role assignment (no rules matched)',
                details: {}
            };

        } catch (error) {
            console.error('[ROLE_ASSIGNMENT] Error creating default role assignment:', error);
            return null;
        }
    }

    /**
     * Apply role assignments to a user
     */
    async applyRoleAssignments(member, assignments, reason = 'Form application approved') {
        try {
            const results = [];
            
            for (const assignment of assignments) {
                try {
                    const role = member.guild.roles.cache.get(assignment.roleId);
                    if (!role) {
                        results.push({
                            assignment,
                            success: false,
                            error: 'Role not found'
                        });
                        continue;
                    }

                    // Check if user already has the role
                    if (member.roles.cache.has(role.id)) {
                        results.push({
                            assignment,
                            success: true,
                            skipped: true,
                            reason: 'User already has this role'
                        });
                        continue;
                    }

                    // Add the role
                    await member.roles.add(role, reason);
                    
                    results.push({
                        assignment,
                        success: true,
                        applied: true
                    });

                    console.log(`[ROLE_ASSIGNMENT] Applied role ${role.name} to ${member.user.tag}`);

                } catch (error) {
                    console.error(`[ROLE_ASSIGNMENT] Error applying role ${assignment.roleName}:`, error);
                    results.push({
                        assignment,
                        success: false,
                        error: error.message
                    });
                }
            }

            const successful = results.filter(r => r.success).length;
            const failed = results.filter(r => !r.success).length;
            const skipped = results.filter(r => r.skipped).length;

            // Log the application results
            await formApplicationStorage.addAuditLog({
                action: 'roles_applied',
                guildId: member.guild.id,
                userId: member.user.id,
                details: {
                    totalAssignments: assignments.length,
                    successful,
                    failed,
                    skipped,
                    roles: results.map(r => ({
                        roleName: r.assignment.roleName,
                        success: r.success,
                        error: r.error
                    }))
                }
            });

            return {
                success: failed === 0,
                results,
                summary: {
                    total: assignments.length,
                    successful,
                    failed,
                    skipped
                }
            };

        } catch (error) {
            console.error('[ROLE_ASSIGNMENT] Error applying role assignments:', error);
            return {
                success: false,
                error: error.message,
                results: [],
                summary: { total: 0, successful: 0, failed: assignments.length, skipped: 0 }
            };
        }
    }

    /**
     * Remove role assignments from a user
     */
    async removeRoleAssignments(member, assignments, reason = 'Form application revoked') {
        try {
            const results = [];
            
            for (const assignment of assignments) {
                try {
                    const role = member.guild.roles.cache.get(assignment.roleId);
                    if (!role) {
                        results.push({
                            assignment,
                            success: false,
                            error: 'Role not found'
                        });
                        continue;
                    }

                    // Check if user has the role
                    if (!member.roles.cache.has(role.id)) {
                        results.push({
                            assignment,
                            success: true,
                            skipped: true,
                            reason: 'User does not have this role'
                        });
                        continue;
                    }

                    // Remove the role
                    await member.roles.remove(role, reason);
                    
                    results.push({
                        assignment,
                        success: true,
                        removed: true
                    });

                    console.log(`[ROLE_ASSIGNMENT] Removed role ${role.name} from ${member.user.tag}`);

                } catch (error) {
                    console.error(`[ROLE_ASSIGNMENT] Error removing role ${assignment.roleName}:`, error);
                    results.push({
                        assignment,
                        success: false,
                        error: error.message
                    });
                }
            }

            const successful = results.filter(r => r.success).length;
            const failed = results.filter(r => !r.success).length;

            return {
                success: failed === 0,
                results,
                summary: {
                    total: assignments.length,
                    successful,
                    failed
                }
            };

        } catch (error) {
            console.error('[ROLE_ASSIGNMENT] Error removing role assignments:', error);
            return {
                success: false,
                error: error.message,
                results: [],
                summary: { total: 0, successful: 0, failed: assignments.length }
            };
        }
    }
}

// Create singleton instance
const roleAssignmentEngine = new RoleAssignmentEngine();

module.exports = roleAssignmentEngine;
