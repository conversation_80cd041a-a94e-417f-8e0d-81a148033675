/**
 * Form Handler
 * Central coordinator for all form-related operations
 */

const formApplicationStorage = require('./formApplicationStorage');
const formRenderer = require('./formRenderer');
const applicationProcessor = require('./applicationProcessor');
const formVerificationEngine = require('./formVerificationEngine');
const roleAssignmentEngine = require('./roleAssignmentEngine');
const adminReviewSystem = require('./adminReviewSystem');
const questionBuilder = require('./questionBuilder');
const { FormConfigModels, APPLICATION_STATUS } = require('./formConfigModels');

class FormHandler {
    constructor() {
        this.activeOperations = new Map();
        this.setupCleanupInterval();
    }

    /**
     * Setup cleanup interval for active operations
     */
    setupCleanupInterval() {
        setInterval(() => {
            const now = Date.now();
            const maxAge = 60 * 60 * 1000; // 1 hour
            
            for (const [operationId, operation] of this.activeOperations.entries()) {
                if (now - operation.startTime > maxAge) {
                    this.activeOperations.delete(operationId);
                    console.log(`[FORM_HANDLER] Cleaned up expired operation: ${operationId}`);
                }
            }
        }, 10 * 60 * 1000); // Check every 10 minutes
    }

    /**
     * Initialize form system for a guild
     */
    async initializeGuild(guildId) {
        try {
            console.log(`[FORM_HANDLER] Initializing form system for guild ${guildId}`);
            
            // Ensure storage is initialized
            await formApplicationStorage.initializeStorage();
            
            // Get existing configurations
            const configs = await formApplicationStorage.getGuildFormConfigs(guildId);
            
            console.log(`[FORM_HANDLER] Found ${Object.keys(configs).length} existing form configurations`);
            
            return {
                success: true,
                configCount: Object.keys(configs).length
            };
            
        } catch (error) {
            console.error('[FORM_HANDLER] Error initializing guild:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Create a new form configuration
     */
    async createFormConfig(guildId, userId, configData) {
        try {
            const operationId = `create_config_${guildId}_${Date.now()}`;
            this.activeOperations.set(operationId, {
                type: 'create_config',
                guildId,
                userId,
                startTime: Date.now()
            });

            // Create form configuration
            const formConfig = FormConfigModels.createFormConfig({
                ...configData,
                guildId,
                createdBy: userId
            });

            // Validate configuration
            FormConfigModels.validateFormConfig(formConfig);

            // Save to storage
            const configId = await formApplicationStorage.saveFormConfig(guildId, formConfig);

            // Log the creation
            await formApplicationStorage.addAuditLog({
                action: 'form_config_created',
                guildId,
                userId,
                configId,
                details: {
                    formName: formConfig.name,
                    questionCount: formConfig.questions.length,
                    verificationEnabled: formConfig.verificationEnabled
                }
            });

            this.activeOperations.delete(operationId);

            return {
                success: true,
                configId,
                formConfig
            };

        } catch (error) {
            console.error('[FORM_HANDLER] Error creating form config:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Submit a form application
     */
    async submitApplication(formConfigId, guildId, userId, answers, guild) {
        try {
            const operationId = `submit_app_${guildId}_${userId}_${Date.now()}`;
            this.activeOperations.set(operationId, {
                type: 'submit_application',
                guildId,
                userId,
                formConfigId,
                startTime: Date.now()
            });

            // Get form configuration
            const formConfig = await formApplicationStorage.getFormConfig(guildId, formConfigId);
            if (!formConfig) {
                throw new Error('Form configuration not found');
            }

            // Check if form is active
            if (!formConfig.isActive) {
                throw new Error('This form is no longer accepting submissions');
            }

            // Check for existing applications if resubmission is not allowed
            if (!formConfig.allowResubmission) {
                const existingApps = await formApplicationStorage.getApplicationsByGuild(guildId, {
                    configId: formConfigId,
                    userId
                });

                if (existingApps.length > 0) {
                    throw new Error('You have already submitted an application for this form');
                }
            }

            // Get user object
            const user = await guild.client.users.fetch(userId);

            // Submit and process the application
            const result = await applicationProcessor.submitApplication(answers, formConfig, user, guild);

            this.activeOperations.delete(operationId);

            return result;

        } catch (error) {
            console.error('[FORM_HANDLER] Error submitting application:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Process verification data upload
     */
    async processVerificationData(guildId, channelId, data, userId) {
        try {
            const operationId = `verify_data_${guildId}_${channelId}_${Date.now()}`;
            this.activeOperations.set(operationId, {
                type: 'verification_data',
                guildId,
                channelId,
                userId,
                startTime: Date.now()
            });

            // Validate and process the data
            const processedData = this.validateVerificationData(data);

            // Save to storage
            const result = await formApplicationStorage.saveVerificationData(guildId, channelId, processedData);

            // Log the upload
            await formApplicationStorage.addAuditLog({
                action: 'verification_data_uploaded',
                guildId,
                userId,
                details: {
                    channelId,
                    recordCount: processedData.length
                }
            });

            this.activeOperations.delete(operationId);

            return {
                success: true,
                recordCount: processedData.length,
                data: result
            };

        } catch (error) {
            console.error('[FORM_HANDLER] Error processing verification data:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Validate verification data format
     */
    validateVerificationData(data) {
        if (!Array.isArray(data)) {
            throw new Error('Verification data must be an array');
        }

        return data.map((entry, index) => {
            if (typeof entry !== 'object' || entry === null) {
                throw new Error(`Entry ${index + 1} must be an object`);
            }

            // Ensure each entry has required fields
            if (!entry.id) {
                entry.id = `entry_${index + 1}_${Date.now()}`;
            }

            if (!entry.addedAt) {
                entry.addedAt = Date.now();
            }

            return entry;
        });
    }

    /**
     * Get application statistics for a guild
     */
    async getApplicationStats(guildId, filters = {}) {
        try {
            const applications = await formApplicationStorage.getApplicationsByGuild(guildId, filters);
            const configs = await formApplicationStorage.getGuildFormConfigs(guildId);

            const stats = {
                total: applications.length,
                byStatus: {},
                byForm: {},
                recentActivity: {},
                verificationStats: {
                    enabled: 0,
                    passed: 0,
                    failed: 0,
                    averageScore: 0
                }
            };

            // Calculate status distribution
            Object.values(APPLICATION_STATUS).forEach(status => {
                stats.byStatus[status] = applications.filter(app => app.status === status).length;
            });

            // Calculate form distribution
            Object.values(configs).forEach(config => {
                const formApps = applications.filter(app => app.configId === config.id);
                stats.byForm[config.name] = {
                    total: formApps.length,
                    approved: formApps.filter(app => app.status === APPLICATION_STATUS.APPROVED).length,
                    pending: formApps.filter(app => app.status === APPLICATION_STATUS.PENDING).length,
                    rejected: formApps.filter(app => app.status === APPLICATION_STATUS.REJECTED).length
                };
            });

            // Calculate recent activity
            const now = Date.now();
            const timeRanges = {
                '24h': 24 * 60 * 60 * 1000,
                '7d': 7 * 24 * 60 * 60 * 1000,
                '30d': 30 * 24 * 60 * 60 * 1000
            };

            Object.entries(timeRanges).forEach(([period, duration]) => {
                const cutoff = now - duration;
                stats.recentActivity[period] = applications.filter(app => app.submittedAt >= cutoff).length;
            });

            // Calculate verification statistics
            const verificationApps = applications.filter(app => app.verificationResults);
            stats.verificationStats.enabled = verificationApps.length;
            stats.verificationStats.passed = applications.filter(app => app.verificationPassed).length;
            stats.verificationStats.failed = verificationApps.length - stats.verificationStats.passed;

            if (verificationApps.length > 0) {
                const totalScore = verificationApps.reduce((sum, app) => sum + (app.verificationScore || 0), 0);
                stats.verificationStats.averageScore = totalScore / verificationApps.length;
            }

            return {
                success: true,
                stats
            };

        } catch (error) {
            console.error('[FORM_HANDLER] Error getting application stats:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Bulk process applications
     */
    async bulkProcessApplications(guildId, applicationIds, action, userId, reason = '') {
        try {
            const operationId = `bulk_${action}_${guildId}_${Date.now()}`;
            this.activeOperations.set(operationId, {
                type: 'bulk_process',
                guildId,
                userId,
                action,
                applicationIds,
                startTime: Date.now()
            });

            const results = [];
            const errors = [];

            for (const appId of applicationIds) {
                try {
                    const result = await this.processApplicationAction(guildId, appId, action, userId, reason);
                    results.push({ applicationId: appId, success: result.success, result });
                } catch (error) {
                    errors.push({ applicationId: appId, error: error.message });
                }
            }

            // Log bulk operation
            await formApplicationStorage.addAuditLog({
                action: `bulk_${action}`,
                guildId,
                userId,
                details: {
                    applicationCount: applicationIds.length,
                    successful: results.filter(r => r.success).length,
                    failed: errors.length,
                    reason
                }
            });

            this.activeOperations.delete(operationId);

            return {
                success: errors.length === 0,
                results,
                errors,
                summary: {
                    total: applicationIds.length,
                    successful: results.filter(r => r.success).length,
                    failed: errors.length
                }
            };

        } catch (error) {
            console.error('[FORM_HANDLER] Error in bulk processing:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Process individual application action
     */
    async processApplicationAction(guildId, applicationId, action, userId, reason = '') {
        try {
            const application = await formApplicationStorage.getApplication(applicationId);
            if (!application || application.guildId !== guildId) {
                throw new Error('Application not found');
            }

            let newStatus;
            switch (action) {
                case 'approve':
                    newStatus = APPLICATION_STATUS.APPROVED;
                    break;
                case 'reject':
                    newStatus = APPLICATION_STATUS.REJECTED;
                    break;
                case 'reopen':
                    newStatus = APPLICATION_STATUS.UNDER_REVIEW;
                    break;
                case 'revoke':
                    newStatus = APPLICATION_STATUS.UNDER_REVIEW;
                    break;
                default:
                    throw new Error(`Unknown action: ${action}`);
            }

            // Update application status
            await formApplicationStorage.updateApplication(applicationId, {
                status: newStatus,
                reviewedBy: userId,
                reviewNotes: reason,
                processedAt: Date.now()
            });

            // Log the action
            await formApplicationStorage.addAuditLog({
                action: `application_${action}`,
                guildId,
                userId,
                applicationId,
                details: {
                    previousStatus: application.status,
                    newStatus,
                    reason
                }
            });

            return {
                success: true,
                newStatus,
                previousStatus: application.status
            };

        } catch (error) {
            console.error(`[FORM_HANDLER] Error processing ${action}:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get active operations for monitoring
     */
    getActiveOperations(guildId = null) {
        const operations = Array.from(this.activeOperations.values());
        return guildId ? operations.filter(op => op.guildId === guildId) : operations;
    }

    /**
     * Get system health status
     */
    async getSystemHealth() {
        try {
            const storageStats = await formApplicationStorage.getStorageStats();
            const activeOps = this.activeOperations.size;
            
            return {
                success: true,
                health: {
                    storage: storageStats,
                    activeOperations: activeOps,
                    uptime: process.uptime(),
                    memoryUsage: process.memoryUsage(),
                    timestamp: Date.now()
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Create singleton instance
const formHandler = new FormHandler();

module.exports = formHandler;
