/**
 * Multiple Forms Test
 * Tests the multiple forms support and button parsing fixes
 */

const setupCommand = require('../commands/setup.js');

class MultipleFormsTest {
    constructor() {
        this.testResults = [];
    }

    /**
     * Run all multiple forms tests
     */
    async runTests() {
        console.log('🧪 Testing Multiple Forms Support and Button Parsing...\n');

        try {
            await this.testButtonParsing();
            await this.testSessionStructure();
            await this.testFormManagement();
            await this.testAddQuestionParsing();

            this.printTestResults();

        } catch (error) {
            console.error('❌ Multiple forms test suite failed:', error);
        }
    }

    /**
     * Test button parsing for add question
     */
    async testButtonParsing() {
        console.log('🔘 Testing Button Parsing...');

        try {
            // Test the specific button ID that was failing
            const buttonId = 'setup_add_question_567939816721874957_1753134010930';
            const parts = buttonId.split('_');
            
            // Replicate the parsing logic from the command
            let action, sessionId, subAction;
            
            if (parts[1] === 'add') {
                action = parts[1];
                subAction = parts[2];
                sessionId = parts.slice(3).join('_');
            }
            
            console.log(`  🔍 Parsing: ${buttonId}`);
            console.log(`    Action: ${action}`);
            console.log(`    SubAction: ${subAction}`);
            console.log(`    SessionId: ${sessionId}`);
            
            // Verify parsing results
            this.assert(action === 'add', 'Action should be "add"');
            this.assert(subAction === 'question', 'SubAction should be "question"');
            this.assert(sessionId === '567939816721874957_1753134010930', 'SessionId should be correct');

            this.addTestResult('Button Parsing', true, 'Add question button parsed correctly');

        } catch (error) {
            this.addTestResult('Button Parsing', false, error.message);
        }
    }

    /**
     * Test session structure for multiple forms
     */
    async testSessionStructure() {
        console.log('📊 Testing Session Structure...');

        try {
            // Mock interaction object
            const mockInteraction = {
                user: { id: 'test_user_123' },
                guild: { id: 'test_guild_456' },
                reply: async () => ({ id: 'mock_message_id' })
            };

            // Create session like the real command does
            const sessionId = `${mockInteraction.user.id}_${Date.now()}`;
            const session = {
                userId: mockInteraction.user.id,
                guildId: mockInteraction.guild.id,
                createdAt: Date.now(),
                currentStep: 'main',
                currentFormId: null,
                forms: new Map(),
                formConfig: null
            };

            const setupSessions = setupCommand.setupSessions;
            setupSessions.set(sessionId, session);

            // Verify session structure
            const retrievedSession = setupSessions.get(sessionId);
            this.assert(retrievedSession !== undefined, 'Session should be stored');
            this.assert(retrievedSession.currentFormId === null, 'currentFormId should be null initially');
            this.assert(retrievedSession.forms instanceof Map, 'forms should be a Map');
            this.assert(retrievedSession.formConfig === null, 'formConfig should be null initially');

            this.addTestResult('Session Structure', true, 'Multiple forms session structure is correct');

        } catch (error) {
            this.addTestResult('Session Structure', false, error.message);
        }
    }

    /**
     * Test form management functionality
     */
    async testFormManagement() {
        console.log('📂 Testing Form Management...');

        try {
            const setupSessions = setupCommand.setupSessions;
            
            // Create test session
            const sessionId = 'test_user_123_' + Date.now();
            const session = {
                userId: 'test_user_123',
                guildId: 'test_guild_456',
                createdAt: Date.now(),
                currentStep: 'main',
                currentFormId: null,
                forms: new Map(),
                formConfig: null
            };

            setupSessions.set(sessionId, session);

            // Test adding forms to session
            const form1 = {
                id: 'form_1',
                name: 'Member Application',
                description: 'Application for new members',
                questions: [],
                isActive: true
            };

            const form2 = {
                id: 'form_2',
                name: 'Event Registration',
                description: 'Registration for events',
                questions: [],
                isActive: false
            };

            session.forms.set('form_1', form1);
            session.forms.set('form_2', form2);

            // Test form selection
            session.currentFormId = 'form_1';
            session.formConfig = form1;

            // Verify form management
            this.assert(session.forms.size === 2, 'Should have 2 forms');
            this.assert(session.currentFormId === 'form_1', 'Current form ID should be set');
            this.assert(session.formConfig.name === 'Member Application', 'Current form should be correct');

            this.addTestResult('Form Management', true, 'Form management functionality working');

        } catch (error) {
            this.addTestResult('Form Management', false, error.message);
        }
    }

    /**
     * Test add question parsing specifically
     */
    async testAddQuestionParsing() {
        console.log('➕ Testing Add Question Parsing...');

        try {
            // Test various add button patterns
            const testCases = [
                {
                    buttonId: 'setup_add_question_567939816721874957_1753134010930',
                    expectedAction: 'add',
                    expectedSubAction: 'question',
                    expectedSessionId: '567939816721874957_1753134010930'
                },
                {
                    buttonId: 'setup_add_role_123456789_987654321',
                    expectedAction: 'add',
                    expectedSubAction: 'role',
                    expectedSessionId: '123456789_987654321'
                }
            ];

            for (const testCase of testCases) {
                const parts = testCase.buttonId.split('_');
                let action, sessionId, subAction;

                // Replicate the parsing logic
                if (parts[1] === 'add') {
                    action = parts[1];
                    subAction = parts[2];
                    sessionId = parts.slice(3).join('_');
                }

                // Verify parsing results
                this.assert(action === testCase.expectedAction, `Action should be ${testCase.expectedAction}, got ${action}`);
                this.assert(subAction === testCase.expectedSubAction, `SubAction should be ${testCase.expectedSubAction}, got ${subAction}`);
                this.assert(sessionId === testCase.expectedSessionId, `SessionId should be ${testCase.expectedSessionId}, got ${sessionId}`);

                console.log(`  ✅ ${testCase.buttonId} -> Action: ${action}, SubAction: ${subAction}, SessionId: ${sessionId}`);
            }

            this.addTestResult('Add Question Parsing', true, 'All add button patterns parsed correctly');

        } catch (error) {
            this.addTestResult('Add Question Parsing', false, error.message);
        }
    }

    /**
     * Add test result
     */
    addTestResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed,
            message
        });

        const status = passed ? '✅' : '❌';
        console.log(`  ${status} ${testName}: ${message}`);
    }

    /**
     * Assert condition
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(`Assertion failed: ${message}`);
        }
    }

    /**
     * Print test results summary
     */
    printTestResults() {
        console.log('\n📊 Multiple Forms Test Results:');
        console.log('==============================');

        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        const failed = total - passed;

        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${failed}`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

        if (failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(r => !r.passed)
                .forEach(r => console.log(`  • ${r.name}: ${r.message}`));
        }

        console.log('\n🎉 Multiple forms test completed!');
        
        if (passed === total) {
            console.log('\n✅ SUCCESS: Multiple forms support is working correctly!');
            console.log('\n🔧 Features Verified:');
            console.log('  • Button parsing for add question actions');
            console.log('  • Multiple forms session structure');
            console.log('  • Form management functionality');
            console.log('  • Add question button parsing');
            console.log('\n🚀 The multiple forms system is ready for use!');
        } else {
            console.log('\n⚠️  Some tests failed. Please review the issues above.');
        }
    }
}

// Export for use
module.exports = MultipleFormsTest;

// Run tests if this file is executed directly
if (require.main === module) {
    const testSuite = new MultipleFormsTest();
    testSuite.runTests().catch(console.error);
}
