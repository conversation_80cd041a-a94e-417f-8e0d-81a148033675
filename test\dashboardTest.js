/**
 * Dashboard Test
 * Tests the fixed dashboard functionality with multiple forms support
 */

const setupCommand = require('../commands/setup.js');

class DashboardTest {
    constructor() {
        this.testResults = [];
    }

    /**
     * Run all dashboard tests
     */
    async runTests() {
        console.log('🧪 Testing Dashboard Functionality...\n');

        try {
            await this.testSessionCreation();
            await this.testDashboardDisplay();
            await this.testFormManagementButtons();
            await this.testConditionalButtons();

            this.printTestResults();

        } catch (error) {
            console.error('❌ Dashboard test suite failed:', error);
        }
    }

    /**
     * Test session creation with new structure
     */
    async testSessionCreation() {
        console.log('📊 Testing Session Creation...');

        try {
            // Mock interaction object
            const mockInteraction = {
                user: { id: 'test_user_123' },
                guild: { id: 'test_guild_456' },
                reply: async (options) => {
                    console.log('  📤 Mock reply sent:', options.embeds?.[0]?.data?.title || 'No embed title');
                    return { id: 'mock_message_id' };
                }
            };

            // Test session creation like the real command does
            const sessionId = `${mockInteraction.user.id}_${Date.now()}`;
            const session = {
                userId: mockInteraction.user.id,
                guildId: mockInteraction.guild.id,
                createdAt: Date.now(),
                currentStep: 'main',
                currentFormId: null,
                forms: new Map(),
                formConfig: null
            };

            const setupSessions = setupCommand.setupSessions;
            setupSessions.set(sessionId, session);

            // Verify session structure
            const retrievedSession = setupSessions.get(sessionId);
            this.assert(retrievedSession !== undefined, 'Session should be stored');
            this.assert(retrievedSession.currentFormId === null, 'currentFormId should be null initially');
            this.assert(retrievedSession.forms instanceof Map, 'forms should be a Map');
            this.assert(retrievedSession.formConfig === null, 'formConfig should be null initially');

            this.addTestResult('Session Creation', true, 'Session created with correct structure');

        } catch (error) {
            this.addTestResult('Session Creation', false, error.message);
        }
    }

    /**
     * Test dashboard display without errors
     */
    async testDashboardDisplay() {
        console.log('🖥️ Testing Dashboard Display...');

        try {
            // Mock interaction object
            const mockInteraction = {
                user: { id: 'test_user_456' },
                guild: { id: 'test_guild_789' },
                reply: async (options) => {
                    console.log('  📤 Dashboard reply sent:', options.embeds?.[0]?.data?.title || 'No embed title');
                    return { id: 'mock_message_id' };
                }
            };

            // Create session
            const sessionId = `${mockInteraction.user.id}_${Date.now()}`;
            const session = {
                userId: mockInteraction.user.id,
                guildId: mockInteraction.guild.id,
                createdAt: Date.now(),
                currentStep: 'main',
                currentFormId: null,
                forms: new Map(),
                formConfig: null
            };

            const setupSessions = setupCommand.setupSessions;
            setupSessions.set(sessionId, session);

            // Mock the loadExistingForms function to avoid storage calls
            const originalLoadExistingForms = setupCommand.loadExistingForms;
            setupCommand.loadExistingForms = async (session) => {
                // Mock implementation - just return without error
                console.log('  🔄 Mock loadExistingForms called');
            };

            // Test dashboard display
            await setupCommand.showMainDashboard(mockInteraction, sessionId, false);

            // Restore original function
            setupCommand.loadExistingForms = originalLoadExistingForms;

            this.addTestResult('Dashboard Display', true, 'Dashboard displayed without errors');

        } catch (error) {
            this.addTestResult('Dashboard Display', false, error.message);
        }
    }

    /**
     * Test form management button creation
     */
    async testFormManagementButtons() {
        console.log('🔘 Testing Form Management Buttons...');

        try {
            // Create test session
            const sessionId = 'test_session_' + Date.now();
            const session = {
                userId: 'test_user_123',
                guildId: 'test_guild_456',
                createdAt: Date.now(),
                currentStep: 'main',
                currentFormId: null,
                forms: new Map(),
                formConfig: null
            };

            // Test button creation
            const components = setupCommand.createMainDashboardComponents(sessionId, session);

            // Verify components structure
            this.assert(Array.isArray(components), 'Components should be an array');
            this.assert(components.length >= 5, 'Should have at least 5 component rows');

            // Check for form management row (first row)
            const formManagementRow = components[0];
            this.assert(formManagementRow.components.length === 3, 'Form management row should have 3 buttons');

            // Check button IDs
            const buttonIds = formManagementRow.components.map(btn => btn.data.custom_id);
            this.assert(buttonIds.includes(`setup_manage_forms_${sessionId}`), 'Should have manage forms button');
            this.assert(buttonIds.includes(`setup_create_form_${sessionId}`), 'Should have create form button');
            this.assert(buttonIds.includes(`setup_select_form_${sessionId}`), 'Should have select form button');

            this.addTestResult('Form Management Buttons', true, 'Form management buttons created correctly');

        } catch (error) {
            this.addTestResult('Form Management Buttons', false, error.message);
        }
    }

    /**
     * Test conditional button states
     */
    async testConditionalButtons() {
        console.log('🔄 Testing Conditional Button States...');

        try {
            // Test with no form selected
            const sessionId1 = 'test_session_no_form_' + Date.now();
            const sessionNoForm = {
                userId: 'test_user_123',
                guildId: 'test_guild_456',
                createdAt: Date.now(),
                currentStep: 'main',
                currentFormId: null,
                forms: new Map(),
                formConfig: null
            };

            const componentsNoForm = setupCommand.createMainDashboardComponents(sessionId1, sessionNoForm);
            const configRowNoForm = componentsNoForm[1]; // Second row is config row

            // Check that config buttons are disabled when no form selected
            const configButtonsDisabled = configRowNoForm.components.every(btn => btn.data.disabled === true);
            this.assert(configButtonsDisabled, 'Config buttons should be disabled when no form selected');

            // Test with form selected
            const sessionId2 = 'test_session_with_form_' + Date.now();
            const sessionWithForm = {
                userId: 'test_user_123',
                guildId: 'test_guild_456',
                createdAt: Date.now(),
                currentStep: 'main',
                currentFormId: 'form_123',
                forms: new Map([['form_123', { name: 'Test Form', questions: [] }]]),
                formConfig: { name: 'Test Form', questions: [], targetChannels: [], roleAssignmentRules: [] }
            };

            const componentsWithForm = setupCommand.createMainDashboardComponents(sessionId2, sessionWithForm);
            const configRowWithForm = componentsWithForm[1]; // Second row is config row

            // Check that config buttons are enabled when form is selected
            const configButtonsEnabled = configRowWithForm.components.every(btn => btn.data.disabled !== true);
            this.assert(configButtonsEnabled, 'Config buttons should be enabled when form is selected');

            this.addTestResult('Conditional Button States', true, 'Button states change correctly based on form selection');

        } catch (error) {
            this.addTestResult('Conditional Button States', false, error.message);
        }
    }

    /**
     * Add test result
     */
    addTestResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed,
            message
        });

        const status = passed ? '✅' : '❌';
        console.log(`  ${status} ${testName}: ${message}`);
    }

    /**
     * Assert condition
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(`Assertion failed: ${message}`);
        }
    }

    /**
     * Print test results summary
     */
    printTestResults() {
        console.log('\n📊 Dashboard Test Results:');
        console.log('============================');

        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        const failed = total - passed;

        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${failed}`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

        if (failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(r => !r.passed)
                .forEach(r => console.log(`  • ${r.name}: ${r.message}`));
        }

        console.log('\n🎉 Dashboard test completed!');
        
        if (passed === total) {
            console.log('\n✅ SUCCESS: Dashboard functionality is working correctly!');
            console.log('\n🔧 Features Verified:');
            console.log('  • Session creation with multiple forms structure');
            console.log('  • Dashboard display without null reference errors');
            console.log('  • Form management button creation');
            console.log('  • Conditional button states based on form selection');
            console.log('\n🚀 The dashboard is ready for production use!');
        } else {
            console.log('\n⚠️  Some tests failed. Please review the issues above.');
        }
    }
}

// Export for use
module.exports = DashboardTest;

// Run tests if this file is executed directly
if (require.main === module) {
    const testSuite = new DashboardTest();
    testSuite.runTests().catch(console.error);
}
