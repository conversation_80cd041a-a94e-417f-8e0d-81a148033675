/**
 * Form Configuration Models and Validation
 * Defines the data structures and validation rules for form configurations
 */

const { v4: uuidv4 } = require('uuid');

/**
 * Question types supported by the form system
 */
const QUESTION_TYPES = {
    TEXT: 'text',
    EMAIL: 'email',
    PHONE: 'phone',
    DATE: 'date',
    NUMBER: 'number',
    DROPDOWN: 'dropdown',
    MULTIPLE_CHOICE: 'multiple_choice',
    CHECKBOX: 'checkbox',
    TEXTAREA: 'textarea'
};

/**
 * Application statuses
 */
const APPLICATION_STATUS = {
    PENDING: 'pending',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    UNDER_REVIEW: 'under_review',
    REQUIRES_VERIFICATION: 'requires_verification'
};

/**
 * Verification criteria types
 */
const VERIFICATION_CRITERIA = {
    EXACT_MATCH: 'exact_match',
    FUZZY_MATCH: 'fuzzy_match',
    CONTAINS: 'contains',
    REGEX: 'regex',
    DATE_RANGE: 'date_range',
    NUMBER_RANGE: 'number_range'
};

/**
 * Role assignment conditions
 */
const ROLE_ASSIGNMENT_CONDITIONS = {
    ANSWER_EQUALS: 'answer_equals',
    ANSWER_CONTAINS: 'answer_contains',
    ANSWER_IN_LIST: 'answer_in_list',
    MULTIPLE_ANSWERS: 'multiple_answers',
    VERIFICATION_PASSED: 'verification_passed',
    CUSTOM_LOGIC: 'custom_logic'
};

class FormConfigModels {
    /**
     * Create a new question configuration
     */
    static createQuestion(options = {}) {
        const question = {
            id: options.id || uuidv4(),
            type: options.type || QUESTION_TYPES.TEXT,
            label: options.label || '',
            description: options.description || '',
            required: options.required !== false,
            placeholder: options.placeholder || '',
            validation: options.validation || {},
            options: options.options || [], // For dropdown/multiple choice
            isVerificationCriteria: options.isVerificationCriteria || false,
            verificationConfig: options.verificationConfig || null,
            order: options.order || 0,
            createdAt: Date.now(),
            updatedAt: Date.now()
        };

        // Validate question configuration
        this.validateQuestion(question);
        return question;
    }

    /**
     * Create verification configuration for a question
     */
    static createVerificationConfig(options = {}) {
        return {
            type: options.type || VERIFICATION_CRITERIA.FUZZY_MATCH,
            threshold: options.threshold || 70, // Similarity threshold for fuzzy matching
            sourceChannel: options.sourceChannel || null, // Channel ID to pull verification data from
            sourceField: options.sourceField || null, // Field name in verification data
            customLogic: options.customLogic || null, // Custom verification function
            errorMessage: options.errorMessage || 'Verification failed for this field',
            allowManualReview: options.allowManualReview !== false
        };
    }

    /**
     * Create role assignment rule
     */
    static createRoleAssignmentRule(options = {}) {
        return {
            id: options.id || uuidv4(),
            name: options.name || '',
            description: options.description || '',
            condition: options.condition || ROLE_ASSIGNMENT_CONDITIONS.ANSWER_EQUALS,
            questionId: options.questionId || null,
            expectedValue: options.expectedValue || null,
            expectedValues: options.expectedValues || [], // For multiple values
            roleId: options.roleId || null,
            roleName: options.roleName || null, // Fallback if role doesn't exist
            priority: options.priority || 0,
            isActive: options.isActive !== false,
            createdAt: Date.now(),
            updatedAt: Date.now()
        };
    }

    /**
     * Create form configuration
     */
    static createFormConfig(options = {}) {
        const config = {
            id: options.id || uuidv4(),
            guildId: options.guildId || null,
            name: options.name || 'Untitled Form',
            description: options.description || '',
            isActive: options.isActive !== false,
            
            // Channel settings
            targetChannels: options.targetChannels || [], // Where forms can be submitted
            logChannel: options.logChannel || null, // Where logs are sent
            
            // Form structure
            questions: options.questions || [],
            
            // Verification settings
            verificationEnabled: options.verificationEnabled || false,
            verificationChannels: options.verificationChannels || [], // Channels with verification data
            similarityThreshold: options.similarityThreshold || 70,
            strictMatching: options.strictMatching || false, // 85-90% threshold for strict mode
            
            // Role assignment
            roleAssignmentRules: options.roleAssignmentRules || [],
            defaultRole: options.defaultRole || null, // Role assigned if no rules match
            maxRolesPerUser: options.maxRolesPerUser || 1,
            
            // Processing settings
            autoApprove: options.autoApprove || false,
            requireAdminReview: options.requireAdminReview || true,
            allowResubmission: options.allowResubmission || false,
            
            // Notification settings
            sendDMOnApproval: options.sendDMOnApproval !== false,
            sendDMOnRejection: options.sendDMOnRejection !== false,
            approvalMessage: options.approvalMessage || 'Your application has been approved!',
            rejectionMessage: options.rejectionMessage || 'Your application has been rejected.',
            
            // Metadata
            createdBy: options.createdBy || null,
            createdAt: Date.now(),
            updatedAt: Date.now(),
            version: options.version || 1
        };

        // Validate form configuration
        this.validateFormConfig(config);
        return config;
    }

    /**
     * Create application submission
     */
    static createApplication(options = {}) {
        return {
            id: options.id || uuidv4(),
            configId: options.configId || null,
            guildId: options.guildId || null,
            userId: options.userId || null,
            username: options.username || null,
            
            // Form data
            answers: options.answers || {}, // questionId -> answer mapping
            
            // Verification results
            verificationResults: options.verificationResults || {},
            verificationScore: options.verificationScore || 0,
            verificationPassed: options.verificationPassed || false,
            
            // Processing status
            status: options.status || APPLICATION_STATUS.PENDING,
            assignedRoles: options.assignedRoles || [],
            reviewedBy: options.reviewedBy || null,
            reviewNotes: options.reviewNotes || '',
            
            // Timestamps
            submittedAt: Date.now(),
            processedAt: options.processedAt || null,
            updatedAt: Date.now()
        };
    }

    /**
     * Validate question configuration
     */
    static validateQuestion(question) {
        if (!question.id || !question.type || !question.label) {
            throw new Error('Question must have id, type, and label');
        }

        if (!Object.values(QUESTION_TYPES).includes(question.type)) {
            throw new Error(`Invalid question type: ${question.type}`);
        }

        if ([QUESTION_TYPES.DROPDOWN, QUESTION_TYPES.MULTIPLE_CHOICE].includes(question.type)) {
            if (!question.options || question.options.length === 0) {
                throw new Error(`Question type ${question.type} requires options`);
            }
        }

        if (question.isVerificationCriteria && !question.verificationConfig) {
            throw new Error('Verification criteria questions must have verification config');
        }

        return true;
    }

    /**
     * Validate form configuration
     */
    static validateFormConfig(config) {
        if (!config.id || !config.guildId || !config.name) {
            throw new Error('Form config must have id, guildId, and name');
        }

        if (!Array.isArray(config.questions)) {
            throw new Error('Questions must be an array');
        }

        // Validate each question
        config.questions.forEach(question => {
            this.validateQuestion(question);
        });

        // Validate role assignment rules
        if (config.roleAssignmentRules) {
            config.roleAssignmentRules.forEach(rule => {
                if (!rule.condition) {
                    throw new Error('Role assignment rules must have condition');
                }

                // Some conditions don't require questionId (e.g., VERIFICATION_PASSED)
                const requiresQuestionId = [
                    ROLE_ASSIGNMENT_CONDITIONS.ANSWER_EQUALS,
                    ROLE_ASSIGNMENT_CONDITIONS.ANSWER_CONTAINS,
                    ROLE_ASSIGNMENT_CONDITIONS.ANSWER_IN_LIST,
                    ROLE_ASSIGNMENT_CONDITIONS.MULTIPLE_ANSWERS
                ];

                if (requiresQuestionId.includes(rule.condition) && !rule.questionId) {
                    throw new Error(`Role assignment rule with condition ${rule.condition} must have questionId`);
                }
            });
        }

        return true;
    }

    /**
     * Validate application submission
     */
    static validateApplication(application, formConfig) {
        if (!application.configId || !application.guildId || !application.userId) {
            throw new Error('Application must have configId, guildId, and userId');
        }

        // Check required questions are answered
        const requiredQuestions = formConfig.questions.filter(q => q.required);
        for (const question of requiredQuestions) {
            if (!application.answers[question.id] || application.answers[question.id].trim() === '') {
                throw new Error(`Required question "${question.label}" is not answered`);
            }
        }

        // Validate answer formats
        for (const [questionId, answer] of Object.entries(application.answers)) {
            const question = formConfig.questions.find(q => q.id === questionId);
            if (question) {
                this.validateAnswer(answer, question);
            }
        }

        return true;
    }

    /**
     * Validate individual answer based on question type
     */
    static validateAnswer(answer, question) {
        switch (question.type) {
            case QUESTION_TYPES.EMAIL:
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(answer)) {
                    throw new Error(`Invalid email format for question "${question.label}"`);
                }
                break;

            case QUESTION_TYPES.PHONE:
                const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
                if (!phoneRegex.test(answer.replace(/[\s\-\(\)]/g, ''))) {
                    throw new Error(`Invalid phone format for question "${question.label}"`);
                }
                break;

            case QUESTION_TYPES.NUMBER:
                if (isNaN(Number(answer))) {
                    throw new Error(`Invalid number format for question "${question.label}"`);
                }
                break;

            case QUESTION_TYPES.DATE:
                if (isNaN(Date.parse(answer))) {
                    throw new Error(`Invalid date format for question "${question.label}"`);
                }
                break;

            case QUESTION_TYPES.DROPDOWN:
            case QUESTION_TYPES.MULTIPLE_CHOICE:
                if (!question.options.some(option => option.value === answer)) {
                    throw new Error(`Invalid option selected for question "${question.label}"`);
                }
                break;
        }

        // Check custom validation rules
        if (question.validation) {
            if (question.validation.minLength && answer.length < question.validation.minLength) {
                throw new Error(`Answer too short for question "${question.label}"`);
            }
            if (question.validation.maxLength && answer.length > question.validation.maxLength) {
                throw new Error(`Answer too long for question "${question.label}"`);
            }
            if (question.validation.pattern && !new RegExp(question.validation.pattern).test(answer)) {
                throw new Error(`Answer doesn't match required pattern for question "${question.label}"`);
            }
        }

        return true;
    }
}

module.exports = {
    FormConfigModels,
    QUESTION_TYPES,
    APPLICATION_STATUS,
    VERIFICATION_CRITERIA,
    ROLE_ASSIGNMENT_CONDITIONS
};
