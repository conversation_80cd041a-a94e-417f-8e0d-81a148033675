{"guilds": {"test_guild_123": {"758338e4-591f-4cd2-8db8-219350246564": {"id": "758338e4-591f-4cd2-8db8-219350246564", "guildId": "test_guild_123", "name": "Storage Test Form", "description": "Testing storage functionality", "isActive": true, "targetChannels": [], "logChannel": null, "questions": [], "verificationEnabled": false, "verificationChannels": [], "similarityThreshold": 70, "strictMatching": false, "roleAssignmentRules": [], "defaultRole": null, "maxRolesPerUser": 1, "autoApprove": false, "requireAdminReview": true, "allowResubmission": false, "sendDMOnApproval": true, "sendDMOnRejection": true, "approvalMessage": "Your application has been approved!", "rejectionMessage": "Your application has been rejected.", "createdBy": null, "createdAt": 1753040368980, "updatedAt": 1753040368981, "version": 1}, "afccadc6-7309-4386-aa6f-9f58f9d2119a": {"id": "afccadc6-7309-4386-aa6f-9f58f9d2119a", "guildId": "test_guild_123", "name": "Simple Test Form", "description": "", "isActive": true, "targetChannels": [], "logChannel": null, "questions": [{"id": "f1cd2c41-3cea-4b70-a2cd-7e5f17347076", "type": "text", "label": "Name", "description": "", "required": true, "placeholder": "", "validation": {}, "options": [], "isVerificationCriteria": false, "verificationConfig": null, "order": 0, "createdAt": 1753040368990, "updatedAt": 1753040368990}], "verificationEnabled": false, "verificationChannels": [], "similarityThreshold": 70, "strictMatching": false, "roleAssignmentRules": [], "defaultRole": null, "maxRolesPerUser": 1, "autoApprove": true, "requireAdminReview": true, "allowResubmission": false, "sendDMOnApproval": true, "sendDMOnRejection": true, "approvalMessage": "Your application has been approved!", "rejectionMessage": "Your application has been rejected.", "createdBy": null, "createdAt": 1753040368990, "updatedAt": 1753040368990, "version": 1}}, "demo_guild_12345": {"fde6eacd-154d-49c7-9aa4-cf7418c01e83": {"id": "fde6eacd-154d-49c7-9aa4-cf7418c01e83", "guildId": "demo_guild_12345", "name": "Member Application", "description": "Apply to become a verified member of our community. All information will be verified against our member database.", "isActive": true, "targetChannels": ["applications", "general"], "logChannel": null, "questions": [{"id": "3f2bb862-9392-4eb8-a3cc-bbf3693699ce", "type": "text", "label": "Full Name", "description": "Enter your full legal name", "required": true, "placeholder": "", "validation": {}, "options": [], "isVerificationCriteria": true, "verificationConfig": {"type": "fuzzy_match", "threshold": 80, "sourceChannel": null, "sourceField": "name", "customLogic": null, "errorMessage": "Verification failed for this field", "allowManualReview": true}, "order": 1, "createdAt": 1753040629479, "updatedAt": 1753040629479}, {"id": "f8d7fca0-2a0b-468f-93b1-83773cbd311d", "type": "email", "label": "Email Address", "description": "Enter your email address for verification", "required": true, "placeholder": "", "validation": {}, "options": [], "isVerificationCriteria": true, "verificationConfig": {"type": "exact_match", "threshold": 70, "sourceChannel": null, "sourceField": "email", "customLogic": null, "errorMessage": "Verification failed for this field", "allowManualReview": true}, "order": 2, "createdAt": 1753040629479, "updatedAt": 1753040629479}, {"id": "be16e846-fb81-4be7-832e-90aceda870a9", "type": "text", "label": "Discord <PERSON>", "description": "Enter your Discord username (e.g., Username#1234)", "required": true, "placeholder": "", "validation": {}, "options": [], "isVerificationCriteria": true, "verificationConfig": {"type": "fuzzy_match", "threshold": 85, "sourceChannel": null, "sourceField": "discord", "customLogic": null, "errorMessage": "Verification failed for this field", "allowManualReview": true}, "order": 3, "createdAt": 1753040629479, "updatedAt": 1753040629479}, {"id": "5d3a789d-cf73-4106-a3f9-193e20182aea", "type": "textarea", "label": "Why do you want to join?", "description": "Tell us why you want to become a member (minimum 50 characters)", "required": true, "placeholder": "", "validation": {"minLength": 50, "maxLength": 500}, "options": [], "isVerificationCriteria": false, "verificationConfig": null, "order": 4, "createdAt": 1753040629479, "updatedAt": 1753040629479}], "verificationEnabled": true, "verificationChannels": ["member_database"], "similarityThreshold": 80, "strictMatching": false, "roleAssignmentRules": [{"id": "92b6143b-94a4-4e86-863a-e319bbb4182c", "name": "Member Role Assignment", "description": "Assign Member role to verified applicants", "condition": "verification_passed", "questionId": null, "expectedValue": null, "expectedValues": [], "roleId": "member_role_123", "roleName": "Member", "priority": 1, "isActive": true, "createdAt": 1753040629479, "updatedAt": 1753040629479}], "defaultRole": null, "maxRolesPerUser": 1, "autoApprove": true, "requireAdminReview": true, "allowResubmission": false, "sendDMOnApproval": true, "sendDMOnRejection": true, "approvalMessage": "Welcome to our community! Your membership has been approved and you have been assigned the Member role.", "rejectionMessage": "Your application has been rejected.", "createdBy": null, "createdAt": 1753040629479, "updatedAt": 1753040629480, "version": 1}, "473bc4a8-2f73-4e6d-88aa-0965fdc341f2": {"id": "473bc4a8-2f73-4e6d-88aa-0965fdc341f2", "guildId": "demo_guild_12345", "name": "Team Selection", "description": "Select your team assignment and get the appropriate roles. This form demonstrates conditional role assignment based on multiple answers.", "isActive": true, "targetChannels": ["team-selection"], "logChannel": null, "questions": [{"id": "4533bdbb-cb8f-40df-9c2e-7d120f5da7f2", "type": "dropdown", "label": "Preferred Team", "description": "Select your preferred team assignment", "required": true, "placeholder": "", "validation": {}, "options": [{"value": "alpha", "label": "Team Alpha - Development"}, {"value": "beta", "label": "Team Beta - Design"}, {"value": "gamma", "label": "Team Gamma - Marketing"}], "isVerificationCriteria": false, "verificationConfig": null, "order": 1, "createdAt": 1753040629482, "updatedAt": 1753040629482}, {"id": "0f60d86b-6b91-4daf-b69c-d3d770f5e158", "type": "multiple_choice", "label": "Experience Level", "description": "What is your experience level in your chosen field?", "required": true, "placeholder": "", "validation": {}, "options": [{"value": "beginner", "label": "<PERSON><PERSON><PERSON> (0-1 years)"}, {"value": "intermediate", "label": "Intermediate (2-5 years)"}, {"value": "advanced", "label": "Advanced (5+ years)"}], "isVerificationCriteria": false, "verificationConfig": null, "order": 2, "createdAt": 1753040629482, "updatedAt": 1753040629482}, {"id": "93350486-6267-4999-b123-7<PERSON><PERSON><PERSON>b6bc9", "type": "checkbox", "label": "Availability", "description": "When are you typically available? (Select all that apply)", "required": true, "placeholder": "", "validation": {}, "options": [{"value": "weekdays", "label": "Weekdays (Mon-Fri)"}, {"value": "weekends", "label": "Weekends (Sat-Sun)"}, {"value": "evenings", "label": "Evenings (6PM-10PM)"}, {"value": "mornings", "label": "Mornings (6AM-12PM)"}], "isVerificationCriteria": false, "verificationConfig": null, "order": 3, "createdAt": 1753040629482, "updatedAt": 1753040629482}], "verificationEnabled": false, "verificationChannels": [], "similarityThreshold": 70, "strictMatching": false, "roleAssignmentRules": [{"id": "80962b98-960d-421a-851c-557fda8c9bfa", "name": "Alpha Team Assignment", "description": "", "condition": "answer_equals", "questionId": "4533bdbb-cb8f-40df-9c2e-7d120f5da7f2", "expectedValue": "alpha", "expectedValues": [], "roleId": "team_alpha_456", "roleName": "Team Alpha", "priority": 2, "isActive": true, "createdAt": 1753040629482, "updatedAt": 1753040629482}, {"id": "74a5a88b-5baf-45be-b6ea-76eaccd3bfb4", "name": "Beta Team Assignment", "description": "", "condition": "answer_equals", "questionId": "4533bdbb-cb8f-40df-9c2e-7d120f5da7f2", "expectedValue": "beta", "expectedValues": [], "roleId": "team_beta_789", "roleName": "Team Beta", "priority": 2, "isActive": true, "createdAt": 1753040629482, "updatedAt": 1753040629482}, {"id": "8047ccb4-bfab-4061-8af8-06a86d7cb431", "name": "Gamma Team Assignment", "description": "", "condition": "answer_equals", "questionId": "4533bdbb-cb8f-40df-9c2e-7d120f5da7f2", "expectedValue": "gamma", "expectedValues": [], "roleId": "team_gamma_012", "roleName": "Team Gamma", "priority": 2, "isActive": true, "createdAt": 1753040629482, "updatedAt": 1753040629482}, {"id": "a61ac949-e173-46c3-a5e1-1295616fccba", "name": "Experienced Member", "description": "", "condition": "answer_equals", "questionId": "0f60d86b-6b91-4daf-b69c-d3d770f5e158", "expectedValue": "advanced", "expectedValues": [], "roleId": "experienced_345", "roleName": "Experienced", "priority": 1, "isActive": true, "createdAt": 1753040629482, "updatedAt": 1753040629482}], "defaultRole": null, "maxRolesPerUser": 3, "autoApprove": true, "requireAdminReview": true, "allowResubmission": false, "sendDMOnApproval": true, "sendDMOnRejection": true, "approvalMessage": "Your team assignment has been processed! Check your roles to see your new team assignment.", "rejectionMessage": "Your application has been rejected.", "createdBy": null, "createdAt": 1753040629482, "updatedAt": 1753040629482, "version": 1}}}}