/**
 * Form Command
 * Allows users to submit applications using configured forms
 */

const {
    SlashCommandBuilder,
    ChannelType,
    MessageFlags,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder
} = require('discord.js');

const formApplicationStorage = require('../utils/formApplicationStorage');
const formRenderer = require('../utils/formRenderer');
const applicationProcessor = require('../utils/applicationProcessor');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('form')
        .setDescription('Submit an application using a configured form')
        .addStringOption(option =>
            option.setName('form')
                .setDescription('The form to submit (leave empty to see available forms)')
                .setRequired(false)),

    async execute(interaction) {
        try {
            const formName = interaction.options.getString('form');
            const guildId = interaction.guild.id;
            const channelId = interaction.channel.id;

            // Get all form configurations for this guild
            const guildConfigs = await formApplicationStorage.getGuildFormConfigs(guildId);
            const activeConfigs = Object.values(guildConfigs).filter(config => 
                config.isActive && 
                (config.targetChannels.length === 0 || config.targetChannels.includes(channelId))
            );

            if (activeConfigs.length === 0) {
                return await interaction.reply({
                    content: '❌ **No Forms Available**\n\nThere are no active forms configured for this channel. Please contact an administrator.',
                    flags: MessageFlags.Ephemeral
                });
            }

            // If no form specified, show available forms
            if (!formName) {
                return await this.showAvailableForms(interaction, activeConfigs);
            }

            // Find the specified form
            const selectedConfig = activeConfigs.find(config => 
                config.name.toLowerCase() === formName.toLowerCase()
            );

            if (!selectedConfig) {
                return await this.showAvailableForms(interaction, activeConfigs, `Form "${formName}" not found.`);
            }

            // Check if user has already submitted an application for this form
            if (!selectedConfig.allowResubmission) {
                const existingApplications = await formApplicationStorage.getApplicationsByGuild(guildId, {
                    configId: selectedConfig.id,
                    userId: interaction.user.id
                });

                if (existingApplications.length > 0) {
                    const latestApp = existingApplications[0];
                    return await interaction.reply({
                        content: `❌ **Application Already Submitted**\n\nYou have already submitted an application for "${selectedConfig.name}".\n\n**Status:** ${this.getStatusDisplay(latestApp.status)}\n**Submitted:** <t:${Math.floor(latestApp.submittedAt / 1000)}:R>\n\nResubmission is not allowed for this form.`,
                        flags: MessageFlags.Ephemeral
                    });
                }
            }

            // Start the form submission process
            await this.startFormSubmission(interaction, selectedConfig);

        } catch (error) {
            console.error('[FORM_COMMAND] Error in form command:', error);
            await interaction.reply({
                content: `❌ **Error**\nFailed to process form command: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Show available forms to the user
     */
    async showAvailableForms(interaction, configs, errorMessage = null) {
        const embed = new EmbedBuilder()
            .setTitle('📋 Available Forms')
            .setDescription(
                (errorMessage ? `❌ ${errorMessage}\n\n` : '') +
                'Select a form to submit your application:\n\n' +
                '**Available Forms:**'
            )
            .setColor(errorMessage ? 0xE74C3C : 0x3498DB)
            .setFooter({ text: 'Form Application System' })
            .setTimestamp();

        // Add form details
        configs.forEach((config, index) => {
            const verificationText = config.verificationEnabled ? '🔍 Verification Required' : '📝 No Verification';
            const reviewText = config.requireAdminReview ? '👥 Admin Review Required' : '⚡ Auto-Processing';
            
            embed.addFields({
                name: `${index + 1}. ${config.name}`,
                value: `${config.description || 'No description provided'}\n\n` +
                       `• **Questions:** ${config.questions.length}\n` +
                       `• **Processing:** ${verificationText}, ${reviewText}\n` +
                       `• **Resubmission:** ${config.allowResubmission ? 'Allowed' : 'Not Allowed'}`,
                inline: false
            });
        });

        // Create selection dropdown
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('form_select')
            .setPlaceholder('Select a form to submit...')
            .addOptions(
                configs.map((config, index) => 
                    new StringSelectMenuOptionBuilder()
                        .setLabel(config.name)
                        .setValue(config.id)
                        .setDescription(`${config.questions.length} questions • ${config.verificationEnabled ? 'Verification required' : 'No verification'}`)
                        .setEmoji('📋')
                )
            );

        const components = [new ActionRowBuilder().addComponents(selectMenu)];

        await interaction.reply({ embeds: [embed], components, flags: MessageFlags.Ephemeral });
    },

    /**
     * Start form submission process
     */
    async startFormSubmission(interaction, formConfig) {
        try {
            // Check if form has questions
            if (formConfig.questions.length === 0) {
                return await interaction.reply({
                    content: '❌ **Form Not Ready**\n\nThis form has no questions configured. Please contact an administrator.',
                    flags: MessageFlags.Ephemeral
                });
            }

            // Show form introduction
            const embed = new EmbedBuilder()
                .setTitle(`📋 ${formConfig.name}`)
                .setDescription(
                    (formConfig.description ? `${formConfig.description}\n\n` : '') +
                    '**Form Information:**\n' +
                    `• **Questions:** ${formConfig.questions.length}\n` +
                    `• **Verification:** ${formConfig.verificationEnabled ? '🔍 Required' : '❌ Not Required'}\n` +
                    `• **Processing:** ${formConfig.autoApprove ? '⚡ Automatic' : '👥 Manual Review'}\n` +
                    `• **Estimated Time:** ${this.estimateFormTime(formConfig.questions.length)}\n\n` +
                    '**Instructions:**\n' +
                    '• Answer all required questions (marked with *)\n' +
                    '• You can navigate back and forth between questions\n' +
                    '• Review your answers before submitting\n' +
                    (formConfig.verificationEnabled ? '• Some answers will be verified against stored data\n' : '') +
                    '• You will receive a notification when your application is processed\n\n' +
                    'Click "Start Application" to begin.'
                )
                .setColor(0x3498DB)
                .setFooter({ text: 'Form Application System' })
                .setTimestamp();

            const startButton = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`form_start_${formConfig.id}`)
                        .setLabel('Start Application')
                        .setStyle(ButtonStyle.Success)
                        .setEmoji('🚀'),
                    new ButtonBuilder()
                        .setCustomId('form_cancel')
                        .setLabel('Cancel')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('❌')
                );

            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [embed], components: [startButton], flags: MessageFlags.Ephemeral });
            } else {
                await interaction.reply({ embeds: [embed], components: [startButton], flags: MessageFlags.Ephemeral });
            }

        } catch (error) {
            console.error('[FORM_COMMAND] Error starting form submission:', error);
            throw error;
        }
    },

    /**
     * Handle form selection from dropdown
     */
    async handleFormSelection(interaction) {
        try {
            const configId = interaction.values[0];
            const formConfig = await formApplicationStorage.getFormConfig(interaction.guild.id, configId);
            
            if (!formConfig) {
                return await interaction.reply({
                    content: '❌ Form configuration not found.',
                    flags: MessageFlags.Ephemeral
                });
            }

            await this.startFormSubmission(interaction, formConfig);

        } catch (error) {
            console.error('[FORM_COMMAND] Error handling form selection:', error);
            await interaction.reply({
                content: `❌ Error loading form: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Handle form start button
     */
    async handleFormStart(interaction) {
        try {
            const configId = interaction.customId.split('_')[2];
            const formConfig = await formApplicationStorage.getFormConfig(interaction.guild.id, configId);
            
            if (!formConfig) {
                return await interaction.reply({
                    content: '❌ Form configuration not found.',
                    flags: MessageFlags.Ephemeral
                });
            }

            // Start the form rendering process
            const formResult = await formRenderer.renderForm(formConfig, interaction);
            
            await interaction.update({
                embeds: formResult.embeds,
                components: formResult.components
            });

        } catch (error) {
            console.error('[FORM_COMMAND] Error handling form start:', error);
            await interaction.reply({
                content: `❌ Error starting form: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    /**
     * Handle form submission completion
     */
    async handleFormSubmission(interaction, sessionId) {
        try {
            const session = formRenderer.getFormSession(sessionId);
            if (!session) {
                return await interaction.reply({
                    content: '❌ Form session expired. Please start over.',
                    flags: MessageFlags.Ephemeral
                });
            }

            // Validate that all required questions are answered
            const requiredQuestions = session.formConfig.questions.filter(q => q.required);
            const missingAnswers = requiredQuestions.filter(q => !session.answers[q.id] || session.answers[q.id].trim() === '');
            
            if (missingAnswers.length > 0) {
                return await interaction.reply({
                    content: `❌ **Missing Required Answers**\n\nPlease answer the following required questions:\n${missingAnswers.map(q => `• ${q.label}`).join('\n')}`,
                    flags: MessageFlags.Ephemeral
                });
            }

            // Show processing message
            await interaction.update({
                embeds: [
                    new EmbedBuilder()
                        .setTitle('⏳ Processing Application...')
                        .setDescription('Your application is being processed. Please wait...')
                        .setColor(0xF39C12)
                ],
                components: []
            });

            // Submit and process the application
            const result = await applicationProcessor.submitApplication(
                session.answers,
                session.formConfig,
                interaction.user,
                interaction.guild
            );

            // Clean up form session
            formRenderer.deleteFormSession(sessionId);

            if (result.success) {
                const statusColor = this.getStatusColor(result.processingResult.status);
                const statusDisplay = this.getStatusDisplay(result.processingResult.status);
                
                const embed = new EmbedBuilder()
                    .setTitle('✅ Application Submitted Successfully!')
                    .setDescription(
                        `Your application for "${session.formConfig.name}" has been submitted and processed.\n\n` +
                        `**Application ID:** \`${result.applicationId}\`\n` +
                        `**Status:** ${statusDisplay}\n` +
                        `**Verification:** ${result.application.verificationPassed ? '✅ Passed' : '❌ Failed'} (${result.application.verificationScore?.toFixed(1) || 0}%)\n` +
                        `**Roles Assigned:** ${result.processingResult.roleAssignments?.length || 0}\n\n` +
                        (result.processingResult.status === 'approved' ? 
                            '🎉 Your application has been approved! You should receive your assigned roles shortly.' :
                            result.processingResult.status === 'under_review' ?
                            '👥 Your application is under review. You will be notified when a decision is made.' :
                            '📋 Your application is being processed. You will be notified of the outcome.')
                    )
                    .setColor(statusColor)
                    .setFooter({ text: 'Form Application System' })
                    .setTimestamp();

                await interaction.followUp({ embeds: [embed], flags: MessageFlags.Ephemeral });
            } else {
                await interaction.followUp({
                    content: `❌ **Application Submission Failed**\n${result.error}`,
                    flags: MessageFlags.Ephemeral
                });
            }

        } catch (error) {
            console.error('[FORM_COMMAND] Error handling form submission:', error);
            await interaction.followUp({
                content: `❌ Error submitting application: ${error.message}`,
                flags: MessageFlags.Ephemeral
            });
        }
    },

    // ==================== UTILITY METHODS ====================

    /**
     * Estimate form completion time
     */
    estimateFormTime(questionCount) {
        const timePerQuestion = 30; // seconds
        const totalSeconds = questionCount * timePerQuestion;
        const minutes = Math.ceil(totalSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''}`;
    },

    /**
     * Get status display text
     */
    getStatusDisplay(status) {
        const displays = {
            'pending': '⏳ Pending',
            'approved': '✅ Approved',
            'rejected': '❌ Rejected',
            'under_review': '👥 Under Review',
            'requires_verification': '🔍 Requires Verification'
        };
        return displays[status] || status;
    },

    /**
     * Get status color
     */
    getStatusColor(status) {
        const colors = {
            'pending': 0xF39C12,
            'approved': 0x27AE60,
            'rejected': 0xE74C3C,
            'under_review': 0x3498DB,
            'requires_verification': 0xE67E22
        };
        return colors[status] || 0x95A5A6;
    }
};

// Export handler functions for use in main bot file
module.exports.handleFormSelection = module.exports.handleFormSelection;
module.exports.handleFormStart = module.exports.handleFormStart;
module.exports.handleFormSubmission = module.exports.handleFormSubmission;
