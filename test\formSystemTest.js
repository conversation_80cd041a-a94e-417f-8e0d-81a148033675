/**
 * Form System Test Suite
 * Comprehensive tests for the form application system
 */

const { FormConfigModels, QUESTION_TYPES, APPLICATION_STATUS, VERIFICATION_CRITERIA, ROLE_ASSIGNMENT_CONDITIONS } = require('../utils/formConfigModels');
const formApplicationStorage = require('../utils/formApplicationStorage');
const formVerificationEngine = require('../utils/formVerificationEngine');
const roleAssignmentEngine = require('../utils/roleAssignmentEngine');
const applicationProcessor = require('../utils/applicationProcessor');

class FormSystemTest {
    constructor() {
        this.testResults = [];
        this.testGuildId = 'test_guild_123';
        this.testUserId = 'test_user_456';
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🧪 Starting Form System Test Suite...\n');

        try {
            await this.testFormConfigModels();
            await this.testFormApplicationStorage();
            await this.testVerificationEngine();
            await this.testRoleAssignmentEngine();
            await this.testApplicationProcessor();

            this.printTestResults();

        } catch (error) {
            console.error('❌ Test suite failed:', error);
        }
    }

    /**
     * Test form configuration models
     */
    async testFormConfigModels() {
        console.log('📝 Testing Form Configuration Models...');

        try {
            // Test question creation
            const question = FormConfigModels.createQuestion({
                type: QUESTION_TYPES.TEXT,
                label: 'Test Question',
                description: 'This is a test question',
                required: true,
                isVerificationCriteria: true,
                verificationConfig: FormConfigModels.createVerificationConfig({
                    type: VERIFICATION_CRITERIA.FUZZY_MATCH,
                    threshold: 80,
                    sourceField: 'name'
                })
            });

            this.assert(question.id, 'Question should have an ID');
            this.assert(question.type === QUESTION_TYPES.TEXT, 'Question type should be TEXT');
            this.assert(question.required === true, 'Question should be required');
            this.assert(question.isVerificationCriteria === true, 'Question should be verification criteria');

            // Test form configuration creation
            const formConfig = FormConfigModels.createFormConfig({
                guildId: this.testGuildId,
                name: 'Test Form',
                description: 'This is a test form',
                questions: [question],
                verificationEnabled: true,
                autoApprove: false,
                requireAdminReview: true
            });

            this.assert(formConfig.id, 'Form config should have an ID');
            this.assert(formConfig.guildId === this.testGuildId, 'Form config should have correct guild ID');
            this.assert(formConfig.questions.length === 1, 'Form config should have one question');

            // Test application creation
            const application = FormConfigModels.createApplication({
                configId: formConfig.id,
                guildId: this.testGuildId,
                userId: this.testUserId,
                username: 'TestUser',
                answers: { [question.id]: 'Test Answer' }
            });

            this.assert(application.id, 'Application should have an ID');
            this.assert(application.configId === formConfig.id, 'Application should have correct config ID');
            this.assert(application.answers[question.id] === 'Test Answer', 'Application should have correct answer');

            // Test validation
            FormConfigModels.validateQuestion(question);
            FormConfigModels.validateFormConfig(formConfig);
            FormConfigModels.validateApplication(application, formConfig);

            this.addTestResult('Form Configuration Models', true, 'All model tests passed');

        } catch (error) {
            this.addTestResult('Form Configuration Models', false, error.message);
        }
    }

    /**
     * Test form application storage
     */
    async testFormApplicationStorage() {
        console.log('💾 Testing Form Application Storage...');

        try {
            // Test storage initialization
            await formApplicationStorage.initializeStorage();

            // Test form config storage
            const testConfig = FormConfigModels.createFormConfig({
                guildId: this.testGuildId,
                name: 'Storage Test Form',
                description: 'Testing storage functionality'
            });

            const configId = await formApplicationStorage.saveFormConfig(this.testGuildId, testConfig);
            this.assert(configId, 'Config should be saved and return an ID');

            const retrievedConfig = await formApplicationStorage.getFormConfig(this.testGuildId, configId);
            this.assert(retrievedConfig, 'Config should be retrievable');
            this.assert(retrievedConfig.name === testConfig.name, 'Retrieved config should match saved config');

            // Test application storage
            const testApplication = FormConfigModels.createApplication({
                configId: configId,
                guildId: this.testGuildId,
                userId: this.testUserId,
                username: 'StorageTestUser',
                answers: { 'test_question': 'test_answer' }
            });

            const applicationId = await formApplicationStorage.saveApplication(testApplication);
            this.assert(applicationId, 'Application should be saved and return an ID');

            const retrievedApplication = await formApplicationStorage.getApplication(applicationId);
            this.assert(retrievedApplication, 'Application should be retrievable');
            this.assert(retrievedApplication.username === testApplication.username, 'Retrieved application should match saved application');

            // Test verification data storage
            const testVerificationData = [
                { name: 'John Doe', email: '<EMAIL>', id: 'verify_1' },
                { name: 'Jane Smith', email: '<EMAIL>', id: 'verify_2' }
            ];

            await formApplicationStorage.saveVerificationData(this.testGuildId, 'test_channel', testVerificationData);
            const retrievedVerificationData = await formApplicationStorage.getVerificationData(this.testGuildId, 'test_channel');
            this.assert(retrievedVerificationData.length === 2, 'Verification data should be saved and retrievable');

            // Test audit logging
            await formApplicationStorage.addAuditLog({
                action: 'test_action',
                guildId: this.testGuildId,
                userId: this.testUserId,
                details: { test: 'data' }
            });

            const auditLogs = await formApplicationStorage.getAuditLogs({ guildId: this.testGuildId, limit: 1 });
            this.assert(auditLogs.length > 0, 'Audit log should be created');

            this.addTestResult('Form Application Storage', true, 'All storage tests passed');

        } catch (error) {
            this.addTestResult('Form Application Storage', false, error.message);
        }
    }

    /**
     * Test verification engine
     */
    async testVerificationEngine() {
        console.log('🔍 Testing Verification Engine...');

        try {
            // Create test verification data
            const verificationData = [
                { name: 'John Doe', email: '<EMAIL>' },
                { name: 'Jane Smith', email: '<EMAIL>' },
                { name: 'Bob Johnson', email: '<EMAIL>' }
            ];

            await formApplicationStorage.saveVerificationData(this.testGuildId, 'verify_channel', verificationData);

            // Create test form with verification
            const verificationQuestion = FormConfigModels.createQuestion({
                type: QUESTION_TYPES.TEXT,
                label: 'Full Name',
                isVerificationCriteria: true,
                verificationConfig: FormConfigModels.createVerificationConfig({
                    type: VERIFICATION_CRITERIA.FUZZY_MATCH,
                    threshold: 70,
                    sourceField: 'name'
                })
            });

            const formConfig = FormConfigModels.createFormConfig({
                guildId: this.testGuildId,
                name: 'Verification Test Form',
                questions: [verificationQuestion],
                verificationEnabled: true,
                verificationChannels: ['verify_channel'],
                similarityThreshold: 70
            });

            // Test exact match
            const exactMatchApp = FormConfigModels.createApplication({
                configId: formConfig.id,
                guildId: this.testGuildId,
                userId: this.testUserId,
                answers: { [verificationQuestion.id]: 'John Doe' }
            });

            const exactResult = await formVerificationEngine.verifyApplication(exactMatchApp, formConfig);
            this.assert(exactResult.success, 'Verification should succeed');
            this.assert(exactResult.passed, 'Exact match should pass verification');
            this.assert(exactResult.score >= 90, 'Exact match should have high score');

            // Test fuzzy match
            const fuzzyMatchApp = FormConfigModels.createApplication({
                configId: formConfig.id,
                guildId: this.testGuildId,
                userId: this.testUserId,
                answers: { [verificationQuestion.id]: 'Jon Doe' } // Slight misspelling
            });

            const fuzzyResult = await formVerificationEngine.verifyApplication(fuzzyMatchApp, formConfig);
            this.assert(fuzzyResult.success, 'Fuzzy verification should succeed');
            this.assert(fuzzyResult.score > 0, 'Fuzzy match should have some score');

            // Test no match
            const noMatchApp = FormConfigModels.createApplication({
                configId: formConfig.id,
                guildId: this.testGuildId,
                userId: this.testUserId,
                answers: { [verificationQuestion.id]: 'Completely Different Name' }
            });

            const noMatchResult = await formVerificationEngine.verifyApplication(noMatchApp, formConfig);
            this.assert(noMatchResult.success, 'No match verification should succeed (but not pass)');
            this.assert(!noMatchResult.passed, 'No match should not pass verification');

            this.addTestResult('Verification Engine', true, 'All verification tests passed');

        } catch (error) {
            this.addTestResult('Verification Engine', false, error.message);
        }
    }

    /**
     * Test role assignment engine
     */
    async testRoleAssignmentEngine() {
        console.log('🏷️ Testing Role Assignment Engine...');

        try {
            // Create mock guild object
            const mockGuild = {
                id: this.testGuildId,
                roles: {
                    cache: new Map([
                        ['role_1', { id: 'role_1', name: 'Test Role 1' }],
                        ['role_2', { id: 'role_2', name: 'Test Role 2' }]
                    ])
                }
            };

            // Create test question and role assignment rule
            const testQuestion = FormConfigModels.createQuestion({
                type: QUESTION_TYPES.DROPDOWN,
                label: 'Group Selection',
                options: [
                    { value: 'group1', label: 'Group 1' },
                    { value: 'group2', label: 'Group 2' }
                ]
            });

            const roleRule = FormConfigModels.createRoleAssignmentRule({
                name: 'Group 1 Assignment',
                condition: ROLE_ASSIGNMENT_CONDITIONS.ANSWER_EQUALS,
                questionId: testQuestion.id,
                expectedValue: 'group1',
                roleId: 'role_1',
                roleName: 'Test Role 1',
                priority: 1
            });

            const formConfig = FormConfigModels.createFormConfig({
                guildId: this.testGuildId,
                name: 'Role Assignment Test Form',
                questions: [testQuestion],
                roleAssignmentRules: [roleRule],
                maxRolesPerUser: 3
            });

            // Test role assignment determination
            const testApplication = FormConfigModels.createApplication({
                configId: formConfig.id,
                guildId: this.testGuildId,
                userId: this.testUserId,
                answers: { [testQuestion.id]: 'group1' }
            });

            const assignmentResult = await roleAssignmentEngine.determineRoleAssignments(
                testApplication, 
                formConfig, 
                mockGuild
            );

            this.assert(assignmentResult.success, 'Role assignment determination should succeed');
            this.assert(assignmentResult.assignments.length > 0, 'Should have role assignments');
            this.assert(assignmentResult.assignments[0].roleId === 'role_1', 'Should assign correct role');

            this.addTestResult('Role Assignment Engine', true, 'All role assignment tests passed');

        } catch (error) {
            this.addTestResult('Role Assignment Engine', false, error.message);
        }
    }

    /**
     * Test application processor
     */
    async testApplicationProcessor() {
        console.log('⚡ Testing Application Processor...');

        try {
            // Create mock user and guild
            const mockUser = {
                id: this.testUserId,
                tag: 'TestUser#1234'
            };

            const mockGuild = {
                id: this.testGuildId,
                name: 'Test Guild',
                client: {
                    users: {
                        fetch: async (userId) => mockUser
                    }
                },
                channels: {
                    cache: new Map()
                },
                members: {
                    fetch: async (userId) => ({
                        user: mockUser,
                        roles: {
                            cache: new Map(),
                            add: async () => {},
                            remove: async () => {}
                        }
                    })
                },
                roles: {
                    cache: new Map([
                        ['role_1', { id: 'role_1', name: 'Test Role 1' }]
                    ])
                }
            };

            // Create simple form configuration
            const simpleQuestion = FormConfigModels.createQuestion({
                type: QUESTION_TYPES.TEXT,
                label: 'Name',
                required: true
            });

            const simpleFormConfig = FormConfigModels.createFormConfig({
                guildId: this.testGuildId,
                name: 'Simple Test Form',
                questions: [simpleQuestion],
                verificationEnabled: false,
                autoApprove: true,
                requireAdminReview: false
            });

            // Save form config
            const configId = await formApplicationStorage.saveFormConfig(this.testGuildId, simpleFormConfig);
            simpleFormConfig.id = configId;

            // Test application submission
            const answers = { [simpleQuestion.id]: 'Test User Name' };
            const submissionResult = await applicationProcessor.submitApplication(
                answers,
                simpleFormConfig,
                mockUser,
                mockGuild
            );

            this.assert(submissionResult.success, 'Application submission should succeed');
            this.assert(submissionResult.applicationId, 'Should return application ID');
            this.assert(submissionResult.processingResult, 'Should return processing result');

            this.addTestResult('Application Processor', true, 'All application processor tests passed');

        } catch (error) {
            this.addTestResult('Application Processor', false, error.message);
        }
    }

    /**
     * Add test result
     */
    addTestResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed,
            message
        });

        const status = passed ? '✅' : '❌';
        console.log(`  ${status} ${testName}: ${message}`);
    }

    /**
     * Assert condition
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(`Assertion failed: ${message}`);
        }
    }

    /**
     * Print test results summary
     */
    printTestResults() {
        console.log('\n📊 Test Results Summary:');
        console.log('========================');

        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        const failed = total - passed;

        console.log(`Total Tests: ${total}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${failed}`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

        if (failed > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults
                .filter(r => !r.passed)
                .forEach(r => console.log(`  • ${r.name}: ${r.message}`));
        }

        console.log('\n🎉 Test suite completed!');
    }
}

// Export for use
module.exports = FormSystemTest;

// Run tests if this file is executed directly
if (require.main === module) {
    const testSuite = new FormSystemTest();
    testSuite.runAllTests().catch(console.error);
}
