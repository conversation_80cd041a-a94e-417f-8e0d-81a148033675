/**
 * Question Builder
 * Interactive system for creating and configuring form questions
 */

const {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder
} = require('discord.js');

const { FormConfigModels, QUESTION_TYPES, VERIFICATION_CRITERIA } = require('./formConfigModels');

class QuestionBuilder {
    constructor() {
        this.questionSessions = new Map(); // Store active question building sessions
        this.setupCleanupInterval();
    }

    /**
     * Setup cleanup interval for expired sessions
     */
    setupCleanupInterval() {
        setInterval(() => {
            const now = Date.now();
            const maxAge = 30 * 60 * 1000; // 30 minutes
            
            for (const [sessionId, session] of this.questionSessions.entries()) {
                if (now - session.createdAt > maxAge) {
                    this.questionSessions.delete(sessionId);
                    console.log(`[QUESTION_BUILDER] Cleaned up expired session: ${sessionId}`);
                }
            }
        }, 5 * 60 * 1000); // Check every 5 minutes
    }

    /**
     * Start question creation process
     */
    async startQuestionCreation(interaction, setupSessionId) {
        const questionSessionId = `${interaction.user.id}_question_${Date.now()}`;
        
        const session = {
            sessionId: questionSessionId,
            setupSessionId,
            userId: interaction.user.id,
            guildId: interaction.guild.id,
            question: FormConfigModels.createQuestion({
                order: 0 // Will be set when added to form
            }),
            step: 'type_selection',
            createdAt: Date.now()
        };
        
        this.questionSessions.set(questionSessionId, session);
        
        return await this.showTypeSelection(interaction, session);
    }

    /**
     * Show question type selection
     */
    async showTypeSelection(interaction, session) {
        const embed = new EmbedBuilder()
            .setTitle('📝 Create New Question - Step 1/4')
            .setDescription(
                'Select the type of question you want to create.\n\n' +
                '**Available Question Types:**'
            )
            .setColor(0x9B59B6)
            .addFields(
                {
                    name: '📝 Text Input',
                    value: 'Simple text responses (names, short answers)',
                    inline: true
                },
                {
                    name: '📧 Email',
                    value: 'Email address with validation',
                    inline: true
                },
                {
                    name: '📞 Phone',
                    value: 'Phone number with validation',
                    inline: true
                },
                {
                    name: '📅 Date',
                    value: 'Date picker input',
                    inline: true
                },
                {
                    name: '🔢 Number',
                    value: 'Numeric input with validation',
                    inline: true
                },
                {
                    name: '📋 Dropdown',
                    value: 'Single selection from dropdown menu',
                    inline: true
                },
                {
                    name: '🔘 Multiple Choice',
                    value: 'Single selection with radio buttons',
                    inline: true
                },
                {
                    name: '☑️ Checkbox',
                    value: 'Multiple selections allowed',
                    inline: true
                },
                {
                    name: '📄 Text Area',
                    value: 'Long text responses (descriptions, essays)',
                    inline: true
                }
            )
            .setFooter({ text: 'Step 1 of 4: Question Type Selection' });

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId(`question_type_${session.sessionId}`)
            .setPlaceholder('Select a question type...')
            .addOptions(
                Object.entries(QUESTION_TYPES).map(([key, value]) => 
                    new StringSelectMenuOptionBuilder()
                        .setLabel(this.getQuestionTypeLabel(value))
                        .setValue(value)
                        .setDescription(this.getQuestionTypeDescription(value))
                        .setEmoji(this.getQuestionTypeEmoji(value))
                )
            );

        const cancelButton = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`question_cancel_${session.sessionId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        const components = [
            new ActionRowBuilder().addComponents(selectMenu),
            cancelButton
        ];

        if (interaction.replied || interaction.deferred) {
            await interaction.followUp({ embeds: [embed], components, ephemeral: true });
        } else {
            await interaction.reply({ embeds: [embed], components, ephemeral: true });
        }

        return session.sessionId;
    }

    /**
     * Show basic question configuration
     */
    async showBasicConfiguration(interaction, session) {
        const embed = new EmbedBuilder()
            .setTitle('📝 Create New Question - Step 2/4')
            .setDescription(
                'Configure the basic properties of your question.\n\n' +
                '**Current Configuration:**'
            )
            .setColor(0x9B59B6)
            .addFields(
                {
                    name: '📝 Question Type',
                    value: this.getQuestionTypeLabel(session.question.type),
                    inline: true
                },
                {
                    name: '🏷️ Label',
                    value: session.question.label || '*Not set*',
                    inline: true
                },
                {
                    name: '📄 Description',
                    value: session.question.description || '*Not set*',
                    inline: true
                },
                {
                    name: '💬 Placeholder',
                    value: session.question.placeholder || '*Not set*',
                    inline: true
                },
                {
                    name: '⚠️ Required',
                    value: session.question.required ? '✅ Yes' : '❌ No',
                    inline: true
                },
                {
                    name: '🔍 Verification Criteria',
                    value: session.question.isVerificationCriteria ? '✅ Yes' : '❌ No',
                    inline: true
                }
            )
            .setFooter({ text: 'Step 2 of 4: Basic Configuration' });

        const components = this.createBasicConfigurationComponents(session);
        await interaction.update({ embeds: [embed], components });
    }

    /**
     * Create basic configuration components
     */
    createBasicConfigurationComponents(session) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`question_set_label_${session.sessionId}`)
                    .setLabel('Set Label')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🏷️'),
                new ButtonBuilder()
                    .setCustomId(`question_set_description_${session.sessionId}`)
                    .setLabel('Set Description')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📄'),
                new ButtonBuilder()
                    .setCustomId(`question_set_placeholder_${session.sessionId}`)
                    .setLabel('Set Placeholder')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('💬')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`question_toggle_required_${session.sessionId}`)
                    .setLabel('Toggle Required')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⚠️'),
                new ButtonBuilder()
                    .setCustomId(`question_toggle_verification_${session.sessionId}`)
                    .setLabel('Toggle Verification')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔍')
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`question_next_step_${session.sessionId}`)
                    .setLabel('Next Step')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('➡️')
                    .setDisabled(!session.question.label),
                new ButtonBuilder()
                    .setCustomId(`question_back_type_${session.sessionId}`)
                    .setLabel('Back')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️'),
                new ButtonBuilder()
                    .setCustomId(`question_cancel_${session.sessionId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        return [row1, row2, row3];
    }

    /**
     * Show options configuration (for dropdown/multiple choice questions)
     */
    async showOptionsConfiguration(interaction, session) {
        const needsOptions = [QUESTION_TYPES.DROPDOWN, QUESTION_TYPES.MULTIPLE_CHOICE, QUESTION_TYPES.CHECKBOX].includes(session.question.type);
        
        if (!needsOptions) {
            // Skip to validation step
            session.step = 'validation';
            return await this.showValidationConfiguration(interaction, session);
        }

        const embed = new EmbedBuilder()
            .setTitle('📝 Create New Question - Step 3/4')
            .setDescription(
                'Configure the options for your question.\n\n' +
                `**Question Type:** ${this.getQuestionTypeLabel(session.question.type)}\n` +
                `**Current Options (${session.question.options.length}):**`
            )
            .setColor(0x9B59B6);

        if (session.question.options.length > 0) {
            const optionsList = session.question.options
                .slice(0, 10) // Show first 10 options
                .map((option, index) => `${index + 1}. **${option.label || option.value}**${option.description ? ` - ${option.description}` : ''}`)
                .join('\n');
            
            embed.addFields({
                name: 'Options',
                value: optionsList,
                inline: false
            });

            if (session.question.options.length > 10) {
                embed.addFields({
                    name: '...',
                    value: `And ${session.question.options.length - 10} more options`,
                    inline: false
                });
            }
        } else {
            embed.addFields({
                name: 'No Options',
                value: 'Click "Add Option" to create your first option.',
                inline: false
            });
        }

        embed.setFooter({ text: 'Step 3 of 4: Options Configuration' });

        const components = this.createOptionsConfigurationComponents(session);
        await interaction.update({ embeds: [embed], components });
    }

    /**
     * Create options configuration components
     */
    createOptionsConfigurationComponents(session) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`question_add_option_${session.sessionId}`)
                    .setLabel('Add Option')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId(`question_edit_option_${session.sessionId}`)
                    .setLabel('Edit Option')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('✏️')
                    .setDisabled(session.question.options.length === 0),
                new ButtonBuilder()
                    .setCustomId(`question_delete_option_${session.sessionId}`)
                    .setLabel('Delete Option')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🗑️')
                    .setDisabled(session.question.options.length === 0)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`question_next_step_${session.sessionId}`)
                    .setLabel('Next Step')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('➡️')
                    .setDisabled(session.question.options.length === 0),
                new ButtonBuilder()
                    .setCustomId(`question_back_basic_${session.sessionId}`)
                    .setLabel('Back')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️'),
                new ButtonBuilder()
                    .setCustomId(`question_cancel_${session.sessionId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        return [row1, row2];
    }

    /**
     * Show validation configuration
     */
    async showValidationConfiguration(interaction, session) {
        const embed = new EmbedBuilder()
            .setTitle('📝 Create New Question - Step 4/4')
            .setDescription(
                'Configure validation rules and verification settings for your question.\n\n' +
                '**Current Configuration:**'
            )
            .setColor(0x9B59B6)
            .addFields(
                {
                    name: '📏 Min Length',
                    value: session.question.validation?.minLength?.toString() || '*Not set*',
                    inline: true
                },
                {
                    name: '📏 Max Length',
                    value: session.question.validation?.maxLength?.toString() || '*Not set*',
                    inline: true
                },
                {
                    name: '🔍 Verification Criteria',
                    value: session.question.isVerificationCriteria ? '✅ Enabled' : '❌ Disabled',
                    inline: true
                }
            );

        if (session.question.isVerificationCriteria && session.question.verificationConfig) {
            embed.addFields(
                {
                    name: '🔍 Verification Type',
                    value: session.question.verificationConfig.type || '*Not set*',
                    inline: true
                },
                {
                    name: '📊 Threshold',
                    value: `${session.question.verificationConfig.threshold || 70}%`,
                    inline: true
                },
                {
                    name: '📺 Source Field',
                    value: session.question.verificationConfig.sourceField || '*Not set*',
                    inline: true
                }
            );
        }

        embed.setFooter({ text: 'Step 4 of 4: Validation & Verification' });

        const components = this.createValidationConfigurationComponents(session);
        await interaction.update({ embeds: [embed], components });
    }

    /**
     * Create validation configuration components
     */
    createValidationConfigurationComponents(session) {
        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`question_set_min_length_${session.sessionId}`)
                    .setLabel('Set Min Length')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📏'),
                new ButtonBuilder()
                    .setCustomId(`question_set_max_length_${session.sessionId}`)
                    .setLabel('Set Max Length')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('📏')
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`question_configure_verification_${session.sessionId}`)
                    .setLabel('Configure Verification')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('🔍')
                    .setDisabled(!session.question.isVerificationCriteria)
            );

        const row3 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`question_finish_${session.sessionId}`)
                    .setLabel('Create Question')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅'),
                new ButtonBuilder()
                    .setCustomId(`question_back_options_${session.sessionId}`)
                    .setLabel('Back')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('⬅️'),
                new ButtonBuilder()
                    .setCustomId(`question_cancel_${session.sessionId}`)
                    .setLabel('Cancel')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('❌')
            );

        return [row1, row2, row3];
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Get question type label
     */
    getQuestionTypeLabel(type) {
        const labels = {
            [QUESTION_TYPES.TEXT]: 'Text Input',
            [QUESTION_TYPES.EMAIL]: 'Email',
            [QUESTION_TYPES.PHONE]: 'Phone',
            [QUESTION_TYPES.DATE]: 'Date',
            [QUESTION_TYPES.NUMBER]: 'Number',
            [QUESTION_TYPES.DROPDOWN]: 'Dropdown',
            [QUESTION_TYPES.MULTIPLE_CHOICE]: 'Multiple Choice',
            [QUESTION_TYPES.CHECKBOX]: 'Checkbox',
            [QUESTION_TYPES.TEXTAREA]: 'Text Area'
        };
        return labels[type] || type;
    }

    /**
     * Get question type description
     */
    getQuestionTypeDescription(type) {
        const descriptions = {
            [QUESTION_TYPES.TEXT]: 'Simple text responses',
            [QUESTION_TYPES.EMAIL]: 'Email with validation',
            [QUESTION_TYPES.PHONE]: 'Phone with validation',
            [QUESTION_TYPES.DATE]: 'Date picker input',
            [QUESTION_TYPES.NUMBER]: 'Numeric input',
            [QUESTION_TYPES.DROPDOWN]: 'Single selection dropdown',
            [QUESTION_TYPES.MULTIPLE_CHOICE]: 'Single selection buttons',
            [QUESTION_TYPES.CHECKBOX]: 'Multiple selections',
            [QUESTION_TYPES.TEXTAREA]: 'Long text responses'
        };
        return descriptions[type] || '';
    }

    /**
     * Get question type emoji
     */
    getQuestionTypeEmoji(type) {
        const emojis = {
            [QUESTION_TYPES.TEXT]: '📝',
            [QUESTION_TYPES.EMAIL]: '📧',
            [QUESTION_TYPES.PHONE]: '📞',
            [QUESTION_TYPES.DATE]: '📅',
            [QUESTION_TYPES.NUMBER]: '🔢',
            [QUESTION_TYPES.DROPDOWN]: '📋',
            [QUESTION_TYPES.MULTIPLE_CHOICE]: '🔘',
            [QUESTION_TYPES.CHECKBOX]: '☑️',
            [QUESTION_TYPES.TEXTAREA]: '📄'
        };
        return emojis[type] || '❓';
    }

    /**
     * Get question session
     */
    getQuestionSession(sessionId) {
        return this.questionSessions.get(sessionId);
    }

    /**
     * Update question session
     */
    updateQuestionSession(sessionId, updates) {
        const session = this.questionSessions.get(sessionId);
        if (session) {
            Object.assign(session, updates);
            this.questionSessions.set(sessionId, session);
        }
        return session;
    }

    /**
     * Delete question session
     */
    deleteQuestionSession(sessionId) {
        return this.questionSessions.delete(sessionId);
    }
}

// Create singleton instance
const questionBuilder = new QuestionBuilder();

module.exports = questionBuilder;
