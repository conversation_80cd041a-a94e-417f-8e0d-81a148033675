# Advanced Discord Bot Form Application System

## Overview

This comprehensive form application system provides Discord servers with powerful tools for creating custom application forms, automated verification, intelligent role assignment, and streamlined admin review processes.

## Key Features

### 🔧 Setup Command & Admin Dashboard
- Interactive `/setup` command with comprehensive configuration interface
- Dashboard with buttons and dropdown menus for complete system setup
- Configure target channels, application questions, verification criteria, roles, and logging

### 📝 Question & Criteria Configuration
- Create custom application questions with multiple types:
  - Text Input, Email, Phone, Date, Number
  - Dropdown, Multiple Choice, Checkbox, Text Area
- Mark questions as "verification criteria" for automatic data matching
- Flexible validation rules and custom placeholders

### 🔍 Data Verification System
- Intelligent matching against data stored in designated Discord channels
- Configurable similarity thresholds (default 70%, strict mode 85-90%)
- Multiple matching algorithms:
  - Exact match with automatic handling
  - Levenshtein distance for fuzzy matching
  - Word overlap analysis
  - Substring and contains matching
- Automatic approval for verified applications

### 🏷️ Role Assignment Logic
- Configure multiple roles based on different answer combinations
- Support conditional role assignment with complex logic
- Priority-based rule system with conflict resolution
- Maximum role limits per user with admin confirmation for edge cases

### ⚡ Application Processing
- Automatic acceptance/rejection based on verification results
- Configurable admin review requirements
- DM notifications for applicants with custom messages
- Comprehensive audit trail and logging

### 📊 Logging & Review System
- Detailed logs sent to admin-configured channels
- "Revoke Decision" buttons for admin override
- Batch processing capabilities for multiple applications
- Advanced filtering and search functionality

## Quick Start Guide

### 1. Initial Setup

1. Run `/setup` command in your server
2. Configure basic settings:
   - Form name and description
   - Target channels where forms can be submitted
   - Log channel for admin notifications

### 2. Create Questions

1. In the setup dashboard, click "Manage Questions"
2. Add questions using the question builder:
   - Choose question type
   - Set label, description, and placeholder
   - Mark as required if needed
   - Configure verification criteria if applicable

### 3. Configure Verification (Optional)

1. Upload verification data to designated channels
2. In setup, enable verification and select verification channels
3. Set similarity threshold (70% default, 85-90% for strict matching)
4. Mark relevant questions as verification criteria

### 4. Set Up Role Assignment

1. Create role assignment rules in the setup dashboard
2. Define conditions based on question answers
3. Map answer patterns to specific roles
4. Set priorities for conflicting rules

### 5. Configure Processing Options

1. Choose between automatic and manual review
2. Set up notification messages
3. Configure resubmission policies
4. Test the complete workflow

## Commands Reference

### User Commands

#### `/form [form_name]`
Submit an application using a configured form.
- If no form name is provided, shows available forms
- Guides users through interactive form submission
- Provides real-time validation and progress tracking

### Admin Commands

#### `/setup`
Open the interactive setup dashboard for form configuration.
- Requires "Manage Guild" permission
- Provides comprehensive configuration interface
- Supports multiple form configurations per server

#### `/form-admin review [status] [user] [form]`
Review and manage submitted applications.
- Filter by status, user, or form name
- Interactive dashboard with pagination
- Bulk approval/rejection capabilities

#### `/form-admin stats`
View detailed application statistics and analytics.
- Status distribution and approval rates
- Recent activity metrics
- Form-specific performance data

#### `/form-admin export [format]`
Export application data in CSV or JSON format.
- Complete application details
- Verification results and role assignments
- Audit trail information

#### `/form-admin cleanup [days]`
Clean up old applications (with confirmation).
- Remove applications older than specified days
- Maintains audit trail integrity
- Requires explicit confirmation

## Configuration Examples

### Basic Contact Form

```
Form Name: "Member Application"
Questions:
1. Full Name (Text, Required, Verification)
2. Email Address (Email, Required, Verification)
3. Date of Birth (Date, Required)
4. Why do you want to join? (Text Area, Required)

Verification: Enabled (70% threshold)
Role Assignment: Default "Member" role
Processing: Manual admin review
```

### Advanced Group Assignment

```
Form Name: "Team Selection"
Questions:
1. Discord Username (Text, Required, Verification)
2. Preferred Team (Dropdown: Team A/B/C, Required)
3. Experience Level (Multiple Choice: Beginner/Intermediate/Advanced)
4. Availability (Checkbox: Weekdays/Weekends/Evenings)

Role Assignment Rules:
- Team A selection → "Team Alpha" role
- Team B selection → "Team Beta" role  
- Team C selection → "Team Gamma" role
- Advanced experience → "Experienced" role

Processing: Auto-approve with verification
```

## Verification Data Format

Verification data should be stored in designated channels as structured messages. The bot automatically parses and indexes this data for matching.

### Example Verification Data

```json
[
  {
    "name": "John Doe",
    "email": "<EMAIL>",
    "discord": "JohnD#1234",
    "member_id": "12345"
  },
  {
    "name": "Jane Smith", 
    "email": "<EMAIL>",
    "discord": "JaneS#5678",
    "member_id": "67890"
  }
]
```

## Role Assignment Conditions

### Available Condition Types

1. **Answer Equals**: Exact match with expected value
2. **Answer Contains**: Answer contains specified text
3. **Answer In List**: Answer matches any value in a list
4. **Multiple Answers**: Complex logic across multiple questions
5. **Verification Passed**: Based on verification results
6. **Custom Logic**: Advanced JavaScript-based conditions

### Priority System

- Higher priority rules are evaluated first
- Conflicting rules resolved by priority
- Maximum roles per user enforced
- Admin confirmation for edge cases

## Best Practices

### Security Considerations

1. **Verification Data**: Store sensitive data in private channels
2. **Admin Permissions**: Limit setup access to trusted administrators
3. **Audit Logging**: Regularly review audit logs for suspicious activity
4. **Data Retention**: Configure appropriate cleanup policies

### Performance Optimization

1. **Question Limits**: Keep forms under 20 questions for best UX
2. **Verification Data**: Limit verification datasets to under 10,000 records
3. **Batch Operations**: Use bulk processing for large application volumes
4. **Cache Management**: System automatically manages caching for performance

### User Experience

1. **Clear Instructions**: Provide detailed form descriptions
2. **Progress Indicators**: Built-in progress tracking keeps users engaged
3. **Error Handling**: Comprehensive validation with helpful error messages
4. **Mobile Friendly**: All interfaces work on mobile Discord clients

## Troubleshooting

### Common Issues

#### "Form session expired"
- Sessions expire after 30 minutes of inactivity
- Users need to restart the form submission process
- Consider breaking long forms into shorter sections

#### "Verification failed"
- Check verification data format and field names
- Verify similarity threshold settings
- Review matching algorithm configuration

#### "Role assignment failed"
- Ensure bot has permission to assign roles
- Check role hierarchy (bot role must be higher)
- Verify role IDs in assignment rules

#### "No applications found"
- Check channel permissions for form submission
- Verify form is marked as active
- Review target channel configuration

### Getting Help

1. Check the audit logs for detailed error information
2. Use `/form-admin stats` to verify system health
3. Test with simple configurations before adding complexity
4. Review this documentation for configuration examples

## Advanced Features

### Custom Verification Logic

The system supports custom verification functions for complex matching scenarios:

```javascript
// Example custom verification
function customVerification(answer, verificationData) {
  // Custom matching logic here
  return {
    passed: true/false,
    score: 0-100,
    details: {}
  };
}
```

### Webhook Integration

Configure webhooks for external system integration:
- Application submissions
- Status changes
- Verification results
- Role assignments

### API Access

The system provides internal APIs for advanced integrations:
- Application data export
- Bulk operations
- Statistics and analytics
- Configuration management

## Support and Updates

This form application system is designed to be highly configurable and extensible. Regular updates add new features and improvements based on community feedback.

For technical support or feature requests, contact your server administrators or the bot development team.
